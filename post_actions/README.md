# Add partition indexes to Google log level data temp tables
## Google log level data temp tables
- network_impressions_temp
- network_clicks_temp
- network_active_views_temp
- network_backfill_impressions_temp
- network_backfill_clicks_temp
- network_backfill_active_views_temp
## Google multi key value table
- multi_key_value_google
## Partitions in each table
- `partition_0` for year from file name
- `partition_1` for month from file name
- `partition_2` for day from file name
- `partition_3` for hour from file name
## Add partition indexes to each table
the command should be first run when the table is created
```shell
aws glue create-partition-index \
    --catalog-id 083168087794 \
    --database-name log-level-data \
    --table-name <table name> \
    --partition-index IndexName=datetime,Keys=partition_0,partition_1,partition_2,partition_3
```

