from os import path

from aws_cdk import (
    Stack,
    Tags,
    aws_s3 as s3,
    aws_ec2 as ec2,
    aws_ecs as ecs,
    aws_ssm as ssm,
    aws_iam as iam,
    aws_logs as logs,
    aws_lambda as lambda_,
    RemovalPolicy

)
from constructs import Construct

DATA_LAKE_BUCKET = "business-integration-data-lake"
PROCESSING_BUCKET_NAME = "business-integration-multi-key-value-report"


class BusinessIntegrationInsighterStack(Stack):

    def __init__(self, scope: Construct, construct_id: str, root_dir: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        insighter_commons_layer = lambda_.LayerVersion(
            self,
            "InsighterCommons",
            layer_version_name="insighter-commons",
            removal_policy=RemovalPolicy.DESTROY,
            code=lambda_.Code.from_docker_build(
                path=path.join(root_dir, "layers", "insighter_commons")
            ),
            compatible_runtimes=[lambda_.Runtime.PYTHON_3_12]
        )

        processing_bucket = s3.Bucket.from_bucket_arn(
            self,
            "ProcessingBucket",
            f"arn:aws:s3:::{PROCESSING_BUCKET_NAME}")

        data_lake_bucket = s3.Bucket.from_bucket_arn(
            self,
            "DataLakeBucket",
            f"arn:aws:s3:::{DATA_LAKE_BUCKET}")

        # VPC
        vpc = ec2.Vpc.from_lookup(
            self, "analytics-vpc",
            vpc_id=ssm.StringParameter.value_from_lookup(
                self,
                '/analytics/vpc_id'
            )
        )

        #  ECS cluster
        cluster = ecs.Cluster(
            self,
            "log-level-data-feed-cluster",
            cluster_name="log-level-data-feed-cluster",
            vpc=vpc,
            enable_fargate_capacity_providers=True,
            container_insights=True,
        )

        # Log Group
        log_group = logs.LogGroup(
            self,
            "ecs-log-group",
            log_group_name="/ecs/duckdb",
            removal_policy=RemovalPolicy.DESTROY
        )

        # ECS security group
        ecs_security_group = ec2.SecurityGroup(
            self,
            "duckdb-ecs-security_group",
            vpc=vpc
        )

        ecs_security_group.connections.allow_from(
            ecs_security_group.connections,
            ec2.Port.all_traffic()
        )

        task_execution_role = iam.Role(
            self,
            id="duckdb-task-execution-role",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com")
        )
        task_execution_role.add_managed_policy(
            iam.ManagedPolicy.from_aws_managed_policy_name('service-role/AmazonECSTaskExecutionRolePolicy')
        )
        task_execution_role.add_managed_policy(
            iam.ManagedPolicy.from_aws_managed_policy_name('AmazonEC2ContainerRegistryReadOnly')
        )

        task_role = iam.Role(
            self,
            id="duckdb-task-role",
            assumed_by=iam.ServicePrincipal("ecs-tasks.amazonaws.com")
        )

        processing_bucket.grant_read_write(task_role)
        data_lake_bucket.grant_read(task_role)

        ssm.StringParameter(
            self,
            "InsighterEcsClusterArn",
            parameter_name="/insighter/ecs_cluster_arn",
            string_value=cluster.cluster_arn
        )
        ssm.StringParameter(
            self,
            "InsighterEcsSecurityGroupId",
            parameter_name="/insighter/ecs_security_group_id",
            string_value=ecs_security_group.security_group_id
        )
        ssm.StringParameter(
            self,
            "InsighterTaskRoleArn",
            parameter_name="/insighter/task_role_arn",
            string_value=task_role.role_arn
        )
        ssm.StringParameter(
            self,
            "InsighterTaskExecutionRoleArn",
            parameter_name="/insighter/task_execution_role_arn",
            string_value=task_execution_role.role_arn
        )
        ssm.StringParameter(
            self,
            "InsighterLogGroupName",
            parameter_name="/insighter/log_group_name",
            string_value=log_group.log_group_name
        )
        ssm.StringParameter(
            self,
            "InsighterCommonsLayerArn",
            parameter_name="/insighter/commons_layer_arn",
            string_value=insighter_commons_layer.layer_version_arn
        )

        Tags.of(self).add("App", "insighter")
