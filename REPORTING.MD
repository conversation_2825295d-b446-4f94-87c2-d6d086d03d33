# Report configuration


## General configuration structure
Reports are stored in YAML format in the following structure:

```yaml
name: <DESCRIPTIVE REPORT NAME>
start_date: <DATE IN THE FORMAT %Y-%m-%d> (optional, defaults to today - 31 days)
end_date: <DATE IN THE FORMAT %Y-%m-%d> (optional, defaults to today)
report_timespan: <CURRENT ONLY SUPPORT LAST_DAY, LAST_MONTH AND REPORTING_LIFETIME> (optional, defaults to REPORTING_LIFETIME)
data_sources: <CURRENT ONLY SUPPORT MICROSOFT AND GOOGLE>
  - MICROSOFT
s3_sharing_account_id: ************ (optional, AWS account id to allow S3 read access from)
notification_recipients: (optional, list of email recipients)
  - <EMAIL>
  - <EMAIL>
sql: >
  SELECT * FROM 
  read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**')
```
> **IMPORTANT** to use default value for optional keys, the key must be omitted.

> **IMPORTANT** _bucket_name_, _multi_key_value_microsoft_ and _date_ must be included at all times in order to 
> process only one day at once.

> **IMPORTANT** To join the Google data with Microsoft data, the join key in Microsoft is auction_id_64 and in Google is auction_id.
> The same auction id from Microsoft can show up in Google with a delay of a few hours.
> The execution time of joining all data under multi_key_value_google to one day data from Microsoft takes up to 10 minutes during testing.

Reports are stored under _s3://business-integration-multi-key-value-report/report_definitions/_. The suffix must be _.yaml_ to be picked up automatically.

## Adding a new report with Microsoft data

1. Download an excerpt of the joined Microsoft feed data
```shell
aws s3 cp s3://business-integration-multi-key-value-report/multi_key_value_microsoft/date=2024-07-09/hour=01/20240709_052041_00073_cawgz_0117602c-9628-460e-97d3-830099b97d5e /Users/<USER>/multi_key_value_report/date=2024-07-09/hour=01/20240709_052041_00073_cawgz_0117602c-9628-460e-97d3-830099b97d5e
```

2. Translate the requested report into SQL

### Report requested

- Recipient:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
- Time range:
  - start date: 22.04.2024
  - end date: none
- Report timespan:
  - REPORTING_LIFETIME
- Data sources:
  - MICROSOFT
- Metrics Microsoft:
  - impressions
  - viewed_impressions
  - revenue
-Filter Microsoft:
  - Keys & Values
    - reload (true)
    - branch (abtest, alpha, development, master)
- Dimensions Microsoft:
  - reload
  - branch

### Local SQL statement preparation

```python
import os
import duckdb

conn = duckdb.connect(database=':memory:', read_only=False)

home_directory = "/tmp/duckdb/"
if not os.path.exists(home_directory):
    os.mkdir(home_directory)

conn.query("SET home_directory='/tmp/duckdb/';")
conn.query("SET temp_directory='/tmp/duckdb.tmp';")

(conn.sql(
    "WITH filtered AS (SELECT *, "
    "   unnest(key_values['reload'], recursive := true) AS reload, "
    "   list_filter(unnest(key_values['branch']), branch -> "
    "       list_contains(['abtest', 'master', 'development', 'alpha'], branch)) AS branch "
    "FROM read_parquet('/Users/<USER>/multi_key_value_microsoft/date=2024-07-09/**') "
    "WHERE list_contains(map_keys(key_values), 'branch') "
    "AND list_contains(map_keys(key_values), 'reload')) "
    "SELECT "
    "   date, reload, branch[1] AS branch, SUM(imps) AS impressions, SUM(imps_viewed) AS viewed_impressions, SUM(revenue) AS revenue "
    "FROM filtered "
    "GROUP BY date, branch, reload "
    "ORDER BY date, branch, reload "
).show(max_width=250)
)

conn.close()
```

### Resulting configuration
```yaml
name: Paul Branch Performance Comparison
start_date: 2024-05-03
end_date:
report_timespan: REPORTING_LIFETIME
data_sources:
  - MICROSOFT
notification_recipients:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
sql: >
  WITH filtered AS (SELECT *,
                           unnest(key_values['reload'], recursive := true) AS reload,
                           list_filter(unnest(key_values['branch']), branch ->
                                                                     list_contains(['abtest', 'master', 'development', 'alpha'], branch)) AS branch
                    FROM read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**')
                    WHERE list_contains(map_keys(key_values), 'branch')
                      AND list_contains(map_keys(key_values), 'reload'))
  SELECT
      date, reload, branch[1] AS branch, SUM(imps) AS impressions, SUM(imps_viewed) AS viewed_impressions, SUM(revenue) AS revenue
  FROM filtered
  GROUP BY date, branch, reload
  ORDER BY date, branch, reload

```

## Adding a new report with Microsoft data and Google data

### Report requested

- Recipient:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
- Time range:
  - start date: 22.04.2024
  - end date: none
- Report timespan:
  - REPORTING_LIFETIME
- Data sources:
  - MICROSOFT
  - GOOGLE
- Metrics Microsoft:
  - impressions
  - viewed_impressions
  - revenue
-Filter Microsoft:
  - Keys & Values
    - reload (true)
    - branch (abtest, alpha, development, master)
- Dimensions Microsoft:
  - reload
  - branch
- Metrics Google:
  - measurable_view
  - viewable_view
  - eligible_view
  - click
  - impression
  - revenue

### Resulting configuration
```yaml
name: Paul Branch Performance Comparison
start_date: 2024-11-01
end_date:
report_timespan: REPORTING_LIFETIME
data_sources:
  - MICROSOFT
  - GOOGLE
notification_recipients:
  - <EMAIL>
sql: >
  WITH filtered AS (SELECT *,
                           unnest(key_values['reload'], recursive := true)                                                                as reload,
                           list_filter(unnest(key_values['branch']), branch ->
                                                                     list_contains(['abtest', 'master', 'development', 'alpha'], branch)) as branch
                    FROM read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**')
                    WHERE list_contains(map_keys(key_values), 'branch')
                      AND list_contains(map_keys(key_values), 'reload')
  )
  SELECT
      f.date, f.reload, f.branch[1] as branch, 
      sum(g.impression) as g_impressions, sum(g.click) as g_clicks, sum(g.click) / sum(g.impression) as g_ctr, sum(g.viewable_view) / sum(g.measurable_view) as g_viewability, sum(g.revenue) as g_revenue,
      sum(f.imps) as m_impressions, sum(f.imps_viewed) as m_viewed_impressions, sum(f.revenue) as m_revenue
  FROM filtered f
  LEFT JOIN (
      SELECT auction_id, measurable_view, viewable_view, eligible_view, click, impression, revenue
      FROM read_parquet('s3://{bucket_name}/{multi_key_value_google}/**')
  ) g
  ON CAST(f.auction_id_64 AS VARCHAR) = g.auction_id
  GROUP BY date, branch, reload
  ORDER BY date, branch, reload
```
