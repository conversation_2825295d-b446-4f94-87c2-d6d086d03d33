## Create Athena database
   ```shell
   aws glue create-database --database-input "{\"Name\":\"log-level-data\"}"
   ```

## AWS resources
1. Create Elastic container repositories
   ```shell
   aws ecr create-repository --repository-name duckdb-repository --region eu-central-1
   ```
2. Add lifecycle policy for log level data bucket
   ```shell
   aws s3api put-bucket-lifecycle-configuration --bucket business-integration-log-level-data --lifecycle-configuration file://s3/log_level_data_feed_bucket_lifecycle_configuration.json
   aws s3api put-bucket-lifecycle-configuration --bucket business-integration-multi-key-value-report --lifecycle-configuration file://s3/multi_key_value_report_bucket_lifecycle_configuration.json
   ```
3. Tag buckets
   ```shell
   aws s3api put-bucket-tagging --bucket business-integration-log-level-data --tagging 'TagSet=[{Key=App,Value=insighter}]'
   aws s3api put-bucket-tagging --bucket business-integration-multi-key-value-report --tagging 'TagSet=[{Key=App,Value=insighter}]'
   ```
4. Create report definition path
   ```shell
   aws s3api put-object --bucket business-integration-multi-key-value-report --key report_definitions/  
   ```
## Docker image

1. DuckDB image
    ```shell
    cd bootstrap/
    ./build_docker_image.sh
    ```
   
2. Test the image against all report definitions
    ```shell
    python -m unittest discover bootstrap/tests
    ```
