{"Rules": [{"ID": "Expire Auction Key Value Feed Temporary Data After 3 Days Rule", "Expiration": {"Days": 3}, "Filter": {"Prefix": "auction_kv_labels_feed_parquet_temp/"}, "Status": "Enabled"}, {"ID": "Expire Standard Feed Temporary Data After 3 Days Rule", "Expiration": {"Days": 30}, "Filter": {"Prefix": "standard_feed_parquet_temp/"}, "Status": "Enabled"}, {"ID": "Expire Clicks Temporary Data After 3 Days Rule", "Expiration": {"Days": 3}, "Filter": {"Prefix": "clicks_parquet_temp/"}, "Status": "Enabled"}, {"ID": "Expire Multi Key Value Microsoft Report Data After 31 Days Rule", "Expiration": {"Days": 60}, "Filter": {"Prefix": "multi_key_value_microsoft/"}, "Status": "Enabled"}, {"ID": "Expire Google Network Impressions Temporary Data After 8 Days Rule", "Expiration": {"Days": 8}, "Filter": {"Prefix": "network_impressions_temp/"}, "Status": "Enabled"}, {"ID": "Expire Google Network Backfill Impressions Temporary Data After 8 Days Rule", "Expiration": {"Days": 8}, "Filter": {"Prefix": "network_backfill_impressions_temp/"}, "Status": "Enabled"}, {"ID": "Expire Google Network Clicks Temporary Data After 8 Days Rule", "Expiration": {"Days": 8}, "Filter": {"Prefix": "network_clicks_temp/"}, "Status": "Enabled"}, {"ID": "Expire Google Network Backfill Clicks Temporary Data After 8 Days Rule", "Expiration": {"Days": 8}, "Filter": {"Prefix": "network_backfill_clicks_temp/"}, "Status": "Enabled"}, {"ID": "Expire Google Network Active Views Temporary Data After 8 Days Rule", "Expiration": {"Days": 8}, "Filter": {"Prefix": "network_active_views_temp/"}, "Status": "Enabled"}, {"ID": "Expire Google Network Backfill Active Views Temporary Data After 8 Days Rule", "Expiration": {"Days": 8}, "Filter": {"Prefix": "network_backfill_active_views_temp/"}, "Status": "Enabled"}, {"ID": "Expire Multi Key Value Google Report Data After 31 Days Rule", "Expiration": {"Days": 60}, "Filter": {"Prefix": "multi_key_value_google/"}, "Status": "Enabled"}, {"ID": "Expire AS Dimensions Data After 31 Days Rule", "Expiration": {"Days": 31}, "Filter": {"Prefix": "as_dimensions/"}, "Status": "Enabled"}]}