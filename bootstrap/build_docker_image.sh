#!/usr/bin/env bash

platforms="linux/amd64,linux/arm64"

echo "Getting latest tag version from duckdb"
duckdb_version="1.2.1"
image_name=insighter-duckdb:"${duckdb_version}"

docker buildx build \
  --no-cache \
  --platform "$platforms" \
  --attest=type=sbom \
  --build-arg "DUCKDB_VERSION=v${duckdb_version}" \
  -t "$image_name" . \
&& docker tag ${image_name} 083168087794.dkr.ecr.eu-central-1.amazonaws.com/insighter:${duckdb_version} \
&& aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 083168087794.dkr.ecr.eu-central-1.amazonaws.com \
&& docker push 083168087794.dkr.ecr.eu-central-1.amazonaws.com/insighter:${duckdb_version}

echo "Done!"

