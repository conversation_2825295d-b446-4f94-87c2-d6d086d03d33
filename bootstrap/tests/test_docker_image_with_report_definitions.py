import datetime
import os
import unittest
from pathlib import Path
from tempfile import TemporaryDirectory

import boto3
from strictyaml import load

from insighter_commons import REPORT_DEFINITION_SCHEMA

import docker

from multi_key_value_reports.functions.reporting.register_processing_tasks.lambda_function import \
    LOAD_CREDENTIALS_STATEMENT
from layers.insighter_commons.insighter_commons.constants import get_report_definition_file_paths, TABLE_MULTI_KEY_VALUE_MICROSOFT, \
    TABLE_MULTI_KEY_VALUE_GOOGLE


class TestDockerImageWithReportDefinitions(unittest.TestCase):

    def test_report_run_with_docker_image(self) -> None:
        session = boto3.Session()
        credentials = session.get_credentials()

        client = docker.from_env()
        root_dir = os.path.dirname(Path(os.path.abspath(__file__)).parent.parent)

        for file_path in get_report_definition_file_paths(root_dir=root_dir):
            with open(file_path, "r") as file:
                with TemporaryDirectory(delete=False) as temp_dir:
                    definition = load(file.read(), REPORT_DEFINITION_SCHEMA).data
                    sql = definition['sql']

                    sql = sql.format(
                        date=f"{(datetime.date.today() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")}/hour=00",
                        bucket_name="business-integration-multi-key-value-report",
                        multi_key_value_microsoft=TABLE_MULTI_KEY_VALUE_MICROSOFT,
                        multi_key_value_google=TABLE_MULTI_KEY_VALUE_GOOGLE
                    )

                    host_file_path = f"{temp_dir}"

                    client.containers.run(
                        "insighter-duckdb:1.2.1",
                        platform="linux/amd64",
                        volumes={
                            host_file_path: {
                                "bind": f"{temp_dir}",
                                "mode": "rw"
                            }
                        },
                        environment={
                            "AWS_ACCESS_KEY_ID": credentials.access_key,
                            "AWS_SECRET_ACCESS_KEY": credentials.secret_key,
                            "AWS_SESSION_TOKEN": credentials.token
                        },
                        command=f"""
                        :memory 
                        \"{LOAD_CREDENTIALS_STATEMENT}; 
                            COPY (
                                {sql}
                            ) TO '{temp_dir}/{definition['name']}.csv' (FORMAT csv, DELIMITER ',', HEADER);
                            ;\"
                        """,
                        remove=True,
                        detach=False)

                    print(f"{temp_dir}/{definition['name']}.csv")
                    self.assertTrue(Path(f"{temp_dir}/{definition['name']}.csv").is_file())



if __name__ == '__main__':
    unittest.main()
