FROM --platform=$TARGETARCH ubuntu:24.10 AS base
ARG DUCKDB_VERSION=_provided_by_docker_build_arg_

FROM base AS base-amd64
ARG DUCKDB_ARCH=amd64

FROM base AS base-arm64
ARG DUCKDB_ARCH=aarch64

FROM base-$TARGETARCH  AS install
ADD "https://github.com/duckdb/duckdb/releases/download/${DUCKDB_VERSION}/duckdb_cli-linux-${DUCKDB_ARCH}.zip" duckdb_cli.zip
RUN apt update  \
    && apt -y upgrade \
    && apt install -y --no-install-recommends unzip \
    && unzip duckdb_cli.zip \
    && rm duckdb_cli.zip

FROM gcr.io/distroless/cc:nonroot

COPY --from=install /duckdb /duckdb

USER nonroot
ENTRYPOINT [ "/duckdb" ]
