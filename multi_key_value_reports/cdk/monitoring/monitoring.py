from aws_cdk import (
    Duration,
    aws_athena as athena,
    aws_s3 as s3,
    aws_lambda as lambda_,
    aws_stepfunctions as sfn,
    aws_stepfunctions_tasks as tasks,
    aws_iam as iam,
    aws_ssm as ssm,
    aws_scheduler as scheduler,
    BundlingOptions
)
from constructs import Construct

from insighter_commons import json_value_selectors
from insighter_commons.constants import TABLE_MULTI_KEY_VALUE_MICROSOFT, \
    TABLE_MULTI_KEY_VALUE_GOOGLE, DEFAULT_LAMBDA_HANDLER


class PartitionMonitoring(Construct):
    def __init__(
            self,
            scope: Construct,
            construct_id: str,
            athena_results_bucket: s3.IBucket,
            athena_work_group: athena.CfnWorkGroup,
            log_level_data_database: str,
            monitoring_recipients: str,
            processing_bucket: s3.IBucket
    ):
        super().__init__(scope, construct_id)

        self.athena_results_bucket = athena_results_bucket
        self.processing_bucket = processing_bucket
        self.athena_work_group = athena_work_group
        self.log_level_data_database = log_level_data_database
        self.monitoring_recipients = monitoring_recipients

        workmail_credentials = ssm.StringParameter.from_secure_string_parameter_attributes(
            self,
            "WorkmailCredentials",
            parameter_name='/workmail/noreply'
        )

        partition_monitoring_lambda = lambda_.Function(
            self,
            "PartitionMonitoringLambda",
            function_name="insighter-partition-monitoring",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/monitoring/partition_monitoring",
                bundling=BundlingOptions(
                    image=lambda_.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install --platform manylinux2014_x86_64 --only-binary=:all: --no-cache -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            ),
            environment={
                "HOME": "/tmp",
                "PROCESSING_BUCKET_NAME": processing_bucket.bucket_name,
                "RECIPIENTS": self.monitoring_recipients,
                "smtp_host": "smtp.mail.eu-west-1.awsapps.com",
                "smtp_port": "465"
            },
            timeout=Duration.seconds(600)
        )

        workmail_credentials.grant_read(partition_monitoring_lambda)

        athena_results_bucket.grant_read(partition_monitoring_lambda)
        processing_bucket.grant_read(partition_monitoring_lambda)

        microsoft_monitoring_branch = self.run_microsoft_partition_monitoring_query().next(
            self.monitoring_step("Microsoft Monitoring Email", partition_monitoring_lambda)
        )

        google_monitoring_branch = self.run_google_partition_monitoring_query().next(
            self.monitoring_step("Google Monitoring Email", partition_monitoring_lambda)
        )

        monitoring_definition = sfn.Parallel(
            self, "Monitoring Parallel"
        ).branch(
            microsoft_monitoring_branch
        ).branch(
            google_monitoring_branch
        )

        monitoring_step_function = sfn.StateMachine(
            self,
            "Duckdb",
            state_machine_name="Insighter-Partition-Monitoring",
            definition_body=sfn.DefinitionBody.from_chainable(monitoring_definition)
        )

        scheduler_role = iam.Role(
            self,
            "InsighterMonitoringSchedulerRole",
            assumed_by=iam.ServicePrincipal("scheduler.amazonaws.com")
        )

        scheduler_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "states:StartExecution",
                    "s3:ListBucket",
                    "s3:GetBucketLocation"
                ],
                resources=[
                    monitoring_step_function.state_machine_arn
                ]
            )
        )

        scheduler.CfnSchedule(
            self,
            "MonitoringSchedule",
            name="InsighterMonitoringSchedule",
            schedule_expression="cron(0 8 * * ? *)",
            schedule_expression_timezone="Europe/Berlin",
            flexible_time_window=scheduler.CfnSchedule.FlexibleTimeWindowProperty(
                mode="OFF"
            ),
            target=scheduler.CfnSchedule.TargetProperty(
                arn=monitoring_step_function.state_machine_arn,
                role_arn=scheduler_role.role_arn
            )
        )

    def monitoring_step(self, construct_id, partition_monitoring_lambda):
        return tasks.LambdaInvoke(
            self,
            construct_id,
            lambda_function=partition_monitoring_lambda
        ).add_catch(
            sfn.Succeed(self, f"{construct_id} Result Check Failed")
        )

    def run_microsoft_partition_monitoring_query(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Check if Microsoft partitions are complete",
            # log level data gets deleted after 8 days
            # join feed data is available with a delay of at least 6 hours
            query_string=f"SELECT date AS microsoft_mkvr_date FROM \"{TABLE_MULTI_KEY_VALUE_MICROSOFT}$partitions\" "
                         "WHERE DATE(date) >= (CURRENT_DATE - INTERVAL '8' DAY) "
                         "AND DATE(date) < (CURRENT_DATE - INTERVAL '1' DAY) "
                         "GROUP BY date "
                         "HAVING COUNT(1) < 24",
            query_execution_context=tasks.QueryExecutionContext(
                database_name=self.log_level_data_database
            ),
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        ).add_catch(
            sfn.Succeed(self, "Microsoft partition check failed")
        )

    def run_google_partition_monitoring_query(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Check if Google partitions are complete",
            # google feed files are delivered in Pacific timezone, which is 9 hours behind Germany
            query_string="SELECT CONCAT(partition_0, '-', partition_1, '-', partition_2) AS google_mkvr_date "
                         f"FROM \"{TABLE_MULTI_KEY_VALUE_GOOGLE}$partitions\" "
                         "WHERE DATE(CONCAT(partition_0, '-', partition_1, '-', partition_2)) >= (CURRENT_DATE - INTERVAL '8' DAY) "
                         "AND DATE(CONCAT(partition_0, '-', partition_1, '-', partition_2)) < (CURRENT_DATE - INTERVAL '1' DAY) "
                         "GROUP BY (partition_0, partition_1, partition_2) "
                         "HAVING COUNT(1) < 24",
            query_execution_context=tasks.QueryExecutionContext(
                database_name=self.log_level_data_database
            ),
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        ).add_catch(
            sfn.Succeed(self, "Google partition check failed")
        )
