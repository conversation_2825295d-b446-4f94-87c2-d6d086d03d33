import os

from aws_cdk import (
    <PERSON><PERSON>,
    CfnTag,
    aws_athena as athena,
    Tags,
    aws_s3 as s3,
    aws_iam as iam
)
from constructs import Construct

from insighter.cdk.stack import BusinessIntegrationInsighterStack
from multi_key_value_reports.cdk.conversion.as_dimensions.feed_conversion import AsDimensionsConversion
from multi_key_value_reports.cdk.conversion.google.feed_conversion import GoogleFeedConversion
from multi_key_value_reports.cdk.conversion.microsoft.feed_conversion import MicrosoftFeedConversion
from multi_key_value_reports.cdk.fetching.fetch_google_data import FetchGoogleData
from multi_key_value_reports.cdk.reporting.report_processing import ReportProcessing
from multi_key_value_reports.cdk.monitoring.monitoring import PartitionMonitoring

DOMAIN_NAME = "insighter.axelspringer.systems"
LOG_LEVEL_DATA_FEED_BUCKET_NAME = "business-integration-log-level-data"
LOG_LEVEL_DATA_DATABASE = "log-level-data"
MONITORING_RECIPIENTS = "<EMAIL>"
ATHENA_RESULTS_BUCKET_NAME = f"query-results-athena-{os.environ.get("CDK_DEFAULT_ACCOUNT")}-eu-central-1"
ATHENA_WORK_GROUP_NAME = "insighter-work-group"
DATA_LAKE_BUCKET = "business-integration-data-lake"
PROCESSING_BUCKET_NAME = "business-integration-multi-key-value-report"
REPORT_CONFIGURATIONS_PREFIX = "report_definitions/"


class BusinessIntegrationMultiKeyValueReportStack(Stack):

    def __init__(self, scope: Construct, construct_id: str, root_dir: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        log_level_data_bucket = s3.Bucket.from_bucket_arn(
            self,
            "LogLevelDataFeedsBucket",
            f"arn:aws:s3:::{LOG_LEVEL_DATA_FEED_BUCKET_NAME}")

        athena_results_bucket = s3.Bucket.from_bucket_arn(
            self,
            "AthenaResultsBucket",
            f"arn:aws:s3:::{ATHENA_RESULTS_BUCKET_NAME}")

        processing_bucket = s3.Bucket.from_bucket_arn(
            self,
            "ProcessingBucket",
            f"arn:aws:s3:::{PROCESSING_BUCKET_NAME}")

        athena_work_group = athena.CfnWorkGroup(
            self, "AthenaWorkGroup",
            name=ATHENA_WORK_GROUP_NAME,
            state="ENABLED",
            recursive_delete_option=True,
            tags=[CfnTag(key="App", value="multi-key-value-report")],
            work_group_configuration=athena.CfnWorkGroup.WorkGroupConfigurationProperty(
                result_configuration=athena.CfnWorkGroup.ResultConfigurationProperty(
                    output_location=f"s3://{athena_results_bucket.bucket_name}/mkvr"
                )
            )
        )

        glue_crawler_role = iam.Role(
            self,
            "GlueCrawlerRole",
            role_name="AWSGlueServiceRole-AccessS3Bucket",
            managed_policies=[
                iam.ManagedPolicy.from_aws_managed_policy_name("service-role/AWSGlueServiceRole"),
            ],
            assumed_by=iam.ServicePrincipal("glue.amazonaws.com")
        )

        FetchGoogleData(
            self,
            "FetchGoogleData",
            root_dir=root_dir,
            processing_bucket=processing_bucket
        )

        GoogleFeedConversion(
            self,
            "GoogleFeedConversion",
            glue_crawler_role=glue_crawler_role,
            processing_bucket=processing_bucket,
            log_level_data_database=LOG_LEVEL_DATA_DATABASE,
            athena_work_group=athena_work_group,
            athena_result_bucket=athena_results_bucket
        )

        AsDimensionsConversion(
            self,
            "AsDimensionsConversion",
            processing_bucket=processing_bucket,
            log_level_data_database=LOG_LEVEL_DATA_DATABASE,
            athena_work_group=athena_work_group,
            athena_result_bucket=athena_results_bucket
        )

        MicrosoftFeedConversion(
            self,
            "MicrosoftFeedConversion",
            athena_results_bucket=athena_results_bucket,
            athena_work_group=athena_work_group,
            log_level_data_bucket=log_level_data_bucket,
            log_level_data_database=LOG_LEVEL_DATA_DATABASE,
            processing_bucket=processing_bucket,
            glue_crawler_role=glue_crawler_role
        )

        ReportProcessing(
            self,
            "ReportProcessing",
            root_dir=root_dir,
            domain_name=DOMAIN_NAME,
            processing_bucket=processing_bucket,
            report_configurations_prefix=REPORT_CONFIGURATIONS_PREFIX
        )

        PartitionMonitoring(
            self,
            "PartitionMonitoring",
            athena_results_bucket=athena_results_bucket,
            athena_work_group=athena_work_group,
            log_level_data_database=LOG_LEVEL_DATA_DATABASE,
            monitoring_recipients=MONITORING_RECIPIENTS,
            processing_bucket=processing_bucket
        )

        Tags.of(self).add("App", "insighter")
