from os import path

from constructs import Construct
from aws_cdk import (
    aws_lambda as _lambda,
    aws_s3 as s3,
    aws_iam as iam,
    aws_stepfunctions_tasks as tasks,
    aws_stepfunctions as sfn,
    aws_scheduler as scheduler,
    aws_ssm as ssm,
    Duration, BundlingOptions
)
from insighter_commons.constants import GOOGLE_RAW_DATA_S3_PATH, CLOUD_STORAGE_NAME, \
    GOOGLE_CLOUD_STORAGE_PARAMETER, DEFAULT_LAMBDA_HANDLER


class FetchGoogleData(Construct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        root_dir: str,
        processing_bucket: s3.IBucket
    ):
        super().__init__(scope, construct_id)

        google_ad_manager_client_layer = _lambda.LayerVersion(
            self, 'Google Ad Manager Client Layer',
            layer_version_name='insighter-google-ad-manager-client',
            code=_lambda.Code.from_asset(
                path=path.join(root_dir, "layers", "google_ad_manager_client"),
                bundling=BundlingOptions(
                    image=_lambda.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install --platform manylinux2014_x86_64 --only-binary=:all: --no-cache -r requirements.txt -t /asset-output/python && cp -au . /asset-output/python"
                    ],
                )
            ),
            compatible_runtimes=[_lambda.Runtime.PYTHON_3_12],
        )

        list_unprocessed_files_lambda_function = _lambda.Function(
            self, 'List Unprocessed Files Lambda',
            function_name='insighter-list-unprocessed-google-files',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset(f'{root_dir}/multi_key_value_reports/functions/fetching/list_unprocessed_files'),
            environment={
                "CLOUD_STORAGE_NAME": CLOUD_STORAGE_NAME,
                "GOOGLE_CLOUD_STORAGE_PARAMETER": GOOGLE_CLOUD_STORAGE_PARAMETER
            },
            layers=[google_ad_manager_client_layer],
            timeout=Duration.seconds(60)
        )
        google_cloud_storage_parameter = ssm.StringParameter.from_secure_string_parameter_attributes(self, 'GoogleCloudStorageParameter', parameter_name=GOOGLE_CLOUD_STORAGE_PARAMETER)
        google_cloud_storage_parameter.grant_read(list_unprocessed_files_lambda_function)

        download_and_store_file_lambda_function = _lambda.Function(
            self, 'Download And Store File Lambda',
            function_name='insighter-download-and-store-google-file',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset(f'{root_dir}/multi_key_value_reports/functions/fetching/download_and_store_file'),
            environment={
                "CLOUD_STORAGE_NAME": CLOUD_STORAGE_NAME,
                "PROCESSING_BUCKET_NAME": processing_bucket.bucket_name,
                "GOOGLE_CLOUD_STORAGE_PARAMETER": GOOGLE_CLOUD_STORAGE_PARAMETER,
                "GOOGLE_RAW_DATA_S3_PATH": GOOGLE_RAW_DATA_S3_PATH
            },
            layers=[google_ad_manager_client_layer],
            memory_size=512,
            timeout=Duration.seconds(500)
        )
        google_cloud_storage_parameter.grant_read(download_and_store_file_lambda_function)
        s3_upload_policy = iam.PolicyStatement(
            actions=['s3:PutObject'],
            resources=[f"{processing_bucket.bucket_arn}/{GOOGLE_RAW_DATA_S3_PATH}*"]
        )
        download_and_store_file_lambda_function.add_to_role_policy(statement=s3_upload_policy)

        step_function_role = iam.Role(
            self, "StepFunctionRole",
            assumed_by=iam.ServicePrincipal("states.amazonaws.com")
        )
        step_function_role.add_to_policy(
            iam.PolicyStatement(
                actions=["lambda:InvokeFunction"],
                resources=[
                    list_unprocessed_files_lambda_function.function_arn,
                    download_and_store_file_lambda_function.function_arn
                ]
            )
        )

        list_files_task = tasks.LambdaInvoke(
            self, 'List Unprocessed Files',
            lambda_function=list_unprocessed_files_lambda_function,
            output_path='$.Payload'
        )

        download_and_upload_task = tasks.LambdaInvoke(
            self, 'Download and Upload File',
            lambda_function=download_and_store_file_lambda_function,
            output_path='$.Payload'
        )

        process_files_map = sfn.Map(
            self, 'Download And Upload Each Unprocessed File',
            items_path='$.unprocessed_files',
            item_selector={
                'file_name': sfn.JsonPath.string_at('$$.Map.Item.Value')
            }
        )
        process_files_map.item_processor(download_and_upload_task)

        map_state = sfn.Map(
            self, 'Process Each File Prefix',
            items_path='$.file_prefixes',
            item_selector={
                'file_prefix': sfn.JsonPath.string_at('$$.Map.Item.Value'),
                'time': sfn.JsonPath.string_at('$.time')
            }
        )
        map_state.item_processor(list_files_task.next(process_files_map))

        definition = sfn.Pass(
            self, "Start Fetching Google Data",
            parameters={
                "time": sfn.JsonPath.string_at("$.time"),
                "file_prefixes": [
                    "NetworkImpressions",
                    "NetworkBackfillImpressions",
                    "NetworkClicks",
                    "NetworkBackfillClicks",
                    "NetworkActiveViews",
                    "NetworkBackfillActiveViews"
                ]
            }
        ).next(map_state)

        state_machine = sfn.StateMachine(
            self, 'FetchGoogleDataStateMachine',
            state_machine_name='Insighter-Fetch-Google-Data',
            definition_body=sfn.DefinitionBody.from_chainable(definition),
            role=step_function_role,
            timeout=Duration.minutes(10)
        )

        scheduler_role = iam.Role(
            self,
            "InsighterFetchGoogleDataSchedulerRole",
            assumed_by=iam.ServicePrincipal("scheduler.amazonaws.com")
        )
        scheduler_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "states:StartExecution"
                ],
                resources=[
                    state_machine.state_machine_arn
                ]
            )
        )

        scheduler.CfnSchedule(
            self,
            "FetchGoogleDataSchedule",
            name="InsighterFetchGoogleDataSchedule",
            schedule_expression="cron(0 * * * ? *)",
            schedule_expression_timezone="Europe/Berlin",
            flexible_time_window=scheduler.CfnSchedule.FlexibleTimeWindowProperty(
                mode="OFF"
            ),
            target=scheduler.CfnSchedule.TargetProperty(
                arn=state_machine.state_machine_arn,
                role_arn=scheduler_role.role_arn
            )
        )
