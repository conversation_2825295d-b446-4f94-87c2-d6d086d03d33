from aws_cdk import (
    aws_iam as iam,
    aws_lambda as _lambda,
    aws_s3 as s3,
    aws_athena as athena,
    aws_stepfunctions as sfn,
    aws_stepfunctions_tasks as tasks,
    aws_scheduler as scheduler,
    aws_events as events,
    aws_events_targets as targets,
    Duration, Tags, BundlingOptions, Stack, ArnFormat
)
from constructs import Construct

from insighter_commons import json_value_selectors
from insighter_commons.constants import DEFAULT_LAMBDA_HANDLER


class AsDimensionsConversion(Construct):

    def __init__(
            self,
            scope: Construct,
            construct_id: str,
            processing_bucket: s3.IBucket,
            log_level_data_database: str,
            athena_work_group: athena.CfnWorkGroup,
            athena_result_bucket: s3.IBucket
    ):
        super().__init__(scope, construct_id)

        self.processing_bucket = processing_bucket
        self.athena_work_group = athena_work_group
        self.athena_result_bucket = athena_result_bucket
        self.query_execution_context = tasks.QueryExecutionContext(
            database_name=log_level_data_database
        )

        Tags.of(self).add("InsighterComponent", "AsDimensionsConversion")

        prepare_ensure_table_exists_query_function = _lambda.Function(
            self,"PrepareEnsureAsDimensionTableQueryLambda",
            function_name="insighter-as-dimension-prepare-athena-table-query",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset("multi_key_value_reports/functions/conversion/as_dimensions/prepare_athena_table_definition_query"),
            timeout=Duration.seconds(60),
            environment={
                "PROCESSING_BUCKET_NAME": self.processing_bucket.bucket_name,
            }
        )

        prepare_get_processable_partitions_query_function = _lambda.Function(
            self, "PrepareGetProcessablePartitionsQueryLambda",
            function_name="insighter-as-dimension-prepare-get-processable-partitions-query",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            timeout=Duration.seconds(180),
            code=_lambda.Code.from_asset(
                "multi_key_value_reports/functions/conversion/as_dimensions/prepare_get_processable_partitions_query",
                bundling=BundlingOptions(
                    image=_lambda.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install --no-cache -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            )
        )

        prepare_as_dimensions_conversion_query_function = _lambda.Function(
            self, "PrepareAsDimensionsConversionQueryLambda",
            function_name="insighter-as-dimension-prepare-conversion-query",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset("multi_key_value_reports/functions/conversion/as_dimensions/prepare_conversion_query"),
            timeout=Duration.seconds(300)
        )

        prepare_as_dimensions_update_query_function = _lambda.Function(
            self, "PrepareAsDimensionsUpdateQueryLambda",
            function_name="insighter-as-dimension-prepare-update-query",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset("multi_key_value_reports/functions/conversion/as_dimensions/prepare_as_dimensions_update_query"),
            timeout=Duration.seconds(60)
        )

        read_as_dimensions_update_result_function = _lambda.Function(
            self, "ReadAsDimensionsUpdateResultLambda",
            function_name="insighter-as-dimension-read-update-result",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset("multi_key_value_reports/functions/conversion/as_dimensions/read_as_dimensions_update_result"),
            timeout=Duration.seconds(60)
        )

        check_required_partitions_exist_function = _lambda.Function(
            self, "CheckRequiredPartitionsExistLambda",
            function_name="insighter-as-dimension-check-partitions-exist",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset("multi_key_value_reports/functions/conversion/as_dimensions/check_required_partitions_exist"),
            timeout=Duration.seconds(300)
        )

        definition = self.execute_lambda_function_step("Prepare query 'Ensure as_dimensions table exists'", prepare_ensure_table_exists_query_function).next(
            self.execute_query("Run query 'Ensure as_dimensions table exists'", sfn.JsonPath.string_at(
                json_value_selectors.QUERY_STRING)).next(
                self.execute_lambda_function_step("Prepare query 'Get processable partitions'", prepare_get_processable_partitions_query_function).next(
                    self.execute_query("Run query 'Get processable partitions'", sfn.JsonPath.string_at(
                        json_value_selectors.QUERY_STRING)).next(
                        self.read_athena_result_step(prepare_as_dimensions_conversion_query_function).next(
                            sfn.Map(
                                self, "Map 'Convert as_dimensions'",
                            ).add_catch(sfn.Fail(self, "Error when inserting into as_dimensions")).item_processor(
                                self.execute_query(
                                    "Run query 'Convert as_dimensions'",
                                    sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                                )
                            )
                        )
                    )
                )
            )
        )

        as_dimensions_conversion_step_function = sfn.StateMachine(
            self, "AsDimensionsConversionStepFunction",
            state_machine_name="Insighter-AS-Dimensions",
            definition_body=sfn.DefinitionBody.from_chainable(definition)
        )

        as_dimensions_conversion_step_function.add_to_role_policy(
            statement=iam.PolicyStatement(
                actions=[
                    "glue:GetDatabase",
                    "glue:GetTables",
                    "glue:GetTable",
                ],
                resources=[
                    self.format_glue_arn("database", "datalake_raw"),
                    self.format_glue_arn("table", "datalake_raw/an_buyer_member"),
                    self.format_glue_arn("table", "datalake_raw/an_deals"),
                    self.format_glue_arn("table", "datalake_raw/an_placements"),
                    self.format_glue_arn("table", "datalake_raw/an_line_items"),
                    self.format_glue_arn("table", "datalake_raw/an_advertisers"),
                    self.format_glue_arn("table", "datalake_raw/google_companies")
                ]
            )
        )

        # Create step function for AS Dimensions corrected data updates
        as_dimensions_update_corrected_definition = self.execute_lambda_function_step(
            "Prepare AS Dimensions Update Query",
            prepare_as_dimensions_update_query_function
        ).next(
            self.execute_query(
                "Find Affected AS Dimensions Partitions",
                sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
            )
        )

        as_dimensions_update_corrected_data_step_function = sfn.StateMachine(
            self, "AsDimensionsUpdateCorrectedDataStepFunction",
            state_machine_name="as_dimensions_update_corrected_data",
            definition_body=sfn.DefinitionBody.from_chainable(as_dimensions_update_corrected_definition)
        )

        as_dimensions_update_corrected_data_step_function.add_to_role_policy(
            statement=iam.PolicyStatement(
                actions=[
                    "glue:GetDatabase",
                    "glue:GetTables",
                    "glue:GetTable",
                ],
                resources=[
                    self.format_glue_arn("database", "datalake_raw"),
                    self.format_glue_arn("table", "datalake_raw/an_buyer_member"),
                    self.format_glue_arn("table", "datalake_raw/an_deals"),
                    self.format_glue_arn("table", "datalake_raw/an_placements"),
                    self.format_glue_arn("table", "datalake_raw/an_line_items"),
                    self.format_glue_arn("table", "datalake_raw/an_advertisers"),
                    self.format_glue_arn("table", "datalake_raw/google_companies")
                ]
            )
        )

        scheduler_role = iam.Role(
            self, "InsighterAsDimensionCoversionSchedulerRole",
            assumed_by=iam.ServicePrincipal("scheduler.amazonaws.com"),
        )
        scheduler_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "states:StartExecution"
                ],
                resources=[
                    as_dimensions_conversion_step_function.state_machine_arn
                ]
            )
        )
        scheduler.CfnSchedule(
            self, "AsDimensionConversionSchedule",
            name="InsigherAsDimensionConversionSchedule",
            schedule_expression="cron(0 * * * ? *)",
            schedule_expression_timezone="Europe/Berlin",
            flexible_time_window=scheduler.CfnSchedule.FlexibleTimeWindowProperty(mode="OFF"),
            target=scheduler.CfnSchedule.TargetProperty(
                arn=as_dimensions_conversion_step_function.state_machine_arn,
                role_arn=scheduler_role.role_arn
            )
        )

        # Create EventBridge rule to listen for Google data correction events
        google_correction_rule = events.Rule(
            self, "GoogleDataCorrectionRule",
            rule_name="insighter-google-data-correction-processed",
            event_pattern=events.EventPattern(
                source=["insighter.google.feed.corrected"],
                detail_type=["GoogleDataCorrectionProcessed"]
            )
        )

        # Add the AS Dimensions corrected data step function as a target for the EventBridge rule
        google_correction_rule.add_target(
            targets.SfnStateMachine(
                machine=as_dimensions_update_corrected_data_step_function,
                input=events.RuleTargetInput.from_event_path("$.detail")
            )
        )

    def execute_lambda_function_step(self, construct_id, lambda_function):
        return tasks.LambdaInvoke(
            self,
            construct_id,
            lambda_function=lambda_function,
            output_path=json_value_selectors.PAYLOAD
        )

    def execute_query(self, construct_id, query_string):
        return tasks.AthenaStartQueryExecution(
            self,
            construct_id,
            query_string=query_string,
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def read_athena_result_step(self, lambda_function):
        read_results_step = sfn.DistributedMap(
            self,
            "Read results step",
            item_reader=sfn.S3CsvItemReader(
                bucket=self.athena_result_bucket,
                key=sfn.JsonPath.format(
                    "mkvr/{}.csv", sfn.JsonPath.string_at("$.output.QueryExecution.QueryExecutionId")
                ),
                max_items=5,
            ),
        ).add_catch(sfn.Succeed(self, "Results file is empty"))

        read_results_step.item_processor(
            tasks.LambdaInvoke(
                self,
                "Perpare query 'Convert as_dimensions'",
                lambda_function=lambda_function,
                output_path=json_value_selectors.PAYLOAD
            )
        )

        return read_results_step

    def format_glue_arn(self, resource, resource_name):
        return Stack.of(self).format_arn(service="glue", resource=resource, resource_name=resource_name, arn_format=ArnFormat.SLASH_RESOURCE_NAME)
