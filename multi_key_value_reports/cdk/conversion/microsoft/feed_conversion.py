from aws_cdk import (
    Duration,
    aws_athena as athena,
    aws_s3 as s3,
    aws_s3_notifications as s3n,
    aws_lambda as lambda_,
    aws_stepfunctions as sfn,
    aws_stepfunctions_tasks as tasks,
    aws_glue as glue,
    aws_iam as iam,
    Tags
)
from constructs import Construct

from insighter_commons import json_value_selectors
from insighter_commons.constants import TABLE_MULTI_KEY_VALUE_MICROSOFT, DEFAULT_LAMBDA_HANDLER


class MicrosoftFeedConversion(Construct):

    def __init__(
            self,
            scope: Construct,
            construct_id: str,
            athena_results_bucket: s3.IBucket,
            athena_work_group: athena.CfnWorkGroup,
            log_level_data_bucket: s3.IBucket,
            log_level_data_database: str,
            processing_bucket: s3.IBucket,
            glue_crawler_role: iam.IRole
    ):
        super().__init__(scope, construct_id)

        self.processing_bucket = processing_bucket
        self.athena_results_bucket = athena_results_bucket
        self.athena_work_group = athena_work_group
        self.log_level_data_database = log_level_data_database

        self.query_execution_context = tasks.QueryExecutionContext(
            database_name=log_level_data_database
        )

        Tags.of(self).add("InsighterComponent", "MicrosoftFeedConversion")

        prepare_standard_feed_conversion_query_function = lambda_.Function(
            self,
            "Prepare Standard Feed Conversion Query",
            function_name="insighter-prepare-standard-feed-conversion-query",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/conversion/microsoft/prepare_standard_feed_conversion_query"),
            timeout=Duration.seconds(60)
        )

        prepare_auction_kv_labels_feed_conversion_query_function = lambda_.Function(
            self,
            "Prepare Auction Key Value Labels Feed Conversion Query",
            function_name="insighter-prepare-auction-kv-labels-feed-conversion-query",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/conversion/microsoft/prepare_auction_kv_labels_feed_conversion_query"),
            timeout=Duration.seconds(60)
        )

        prepare_clicks_conversion_query_function = lambda_.Function(
            self,
            "Prepare Clicks Conversion Query",
            function_name="insighter-prepare-clicks-conversion-query",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/conversion/microsoft/prepare_clicks_conversion_query"),
            timeout=Duration.seconds(60)
        )

        prepare_key_value_report_join_query_function = lambda_.Function(
            self,
            "Prepare Key Value Report Join Query",
            function_name="insighter-prepare-key-value-report-join-query",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/conversion/microsoft/prepare_key_value_report_join_query"),
            timeout=Duration.seconds(60),
            environment={
                "TABLE_MULTI_KEY_VALUE_MICROSOFT": TABLE_MULTI_KEY_VALUE_MICROSOFT
            }
        )

        definition = sfn.Choice(
            self,
            "Is Standard Or Auction Key Value Feed?"
        ).when(
            sfn.Condition.or_(
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, "standard_feed"),
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, "auction_kv_labels_feed")
            ),
            self.prepare_partition_query().next(
                self.run_athena_create_partition_query().next(
                    sfn.Choice(
                        self,
                        "Process Standard Or Key Value Feed?"
                    ).when(
                        sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, "standard_feed"),
                        sfn.Parallel(
                            self,
                            "Process Standard Feed",
                            result_path=sfn.JsonPath.DISCARD,
                            output_path=sfn.JsonPath.DISCARD
                        ).branch(
                            self.ensure_temporary_parquet_table_standard_feed_exists().next(
                                self.query_for_unprocessed_partitions("standard_feed",
                                                                      "standard_feed_parquet_temp").next(
                                    self.read_results_step(
                                        "standard_feed",
                                        prepare_standard_feed_conversion_query_function
                                    ).next(
                                        self.run_queries("standard_feed")
                                    )
                                )
                            )
                        ).branch(
                            self.ensure_temporary_parquet_table_clicks_exists().next(
                                self.query_for_unprocessed_partitions("standard_feed", "clicks_parquet_temp").next(
                                    self.read_results_step(
                                        "standard_feed_clicks",
                                        prepare_clicks_conversion_query_function
                                    ).next(
                                        self.run_queries("clicks_feed")
                                    )
                                )
                            )
                        ).next(
                            self.ensure_multi_key_value_report_table_exists().next(
                                self.query_processable_partitions().next(
                                    self.read_results_step(
                                        TABLE_MULTI_KEY_VALUE_MICROSOFT,
                                        prepare_key_value_report_join_query_function
                                    ).next(
                                        self.run_queries(TABLE_MULTI_KEY_VALUE_MICROSOFT)
                                    )
                                )
                            )
                        )
                    ).when(
                        sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, "auction_kv_labels_feed"),
                        self.ensure_temporary_parquet_table_key_value_feed_exists().next(
                            self.query_for_unprocessed_partitions(
                                "auction_kv_labels_feed",
                                "auction_kv_labels_feed_parquet_temp"
                            ).next(
                                self.read_results_step(
                                    "auction_kv_labels_feed",
                                    prepare_auction_kv_labels_feed_conversion_query_function
                                ).next(
                                    self.run_queries("auction_kv_labels_feed")
                                )
                            )
                        )
                    ).otherwise(
                        sfn.Fail(
                            self,
                            "Unknown Table"
                        )
                    )
                )
            )
        ).otherwise(
            sfn.Succeed(
                self,
                "Ignore Feed"
            )
        )

        conversion_step_function = sfn.StateMachine(
            self,
            "MultiKeyValueReport",
            state_machine_name="Insighter-Report-Conversion",
            definition_body=sfn.DefinitionBody.from_chainable(definition)
        )

        prepare_standard_feed_conversion_query_function.grant_invoke(conversion_step_function)
        prepare_auction_kv_labels_feed_conversion_query_function.grant_invoke(conversion_step_function)
        prepare_clicks_conversion_query_function.grant_invoke(conversion_step_function)
        prepare_key_value_report_join_query_function.grant_invoke(conversion_step_function)

        trigger_function = lambda_.Function(
            self,
            "FeedConversionTriggerFunction",
            function_name="insighter-feed-conversion-trigger",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/conversion/microsoft/step_function_trigger"),
            timeout=Duration.seconds(60),
            environment={
                "STATE_MACHINE_ARN": conversion_step_function.state_machine_arn
            }
        )

        log_level_data_bucket.add_event_notification(
            s3.EventType.OBJECT_CREATED,
            s3n.LambdaDestination(trigger_function),
            s3.NotificationKeyFilter(prefix="manifests/", suffix=".json")
        )

        log_level_data_bucket.grant_read(trigger_function)
        processing_bucket.grant_write(conversion_step_function)
        athena_results_bucket.grant_read_write(conversion_step_function)
        conversion_step_function.grant_start_execution(trigger_function)

        log_level_data_bucket.grant_read_write(glue_crawler_role)

        glue.CfnCrawler(
            self,
            "StandardFeedCrawler",
            name="standard_feed",
            role=glue_crawler_role.role_name,
            database_name=log_level_data_database,
            targets=glue.CfnCrawler.TargetsProperty(
                s3_targets=[glue.CfnCrawler.S3TargetProperty(
                    path=f"s3://{log_level_data_bucket.bucket_name}/feeds/standard_feed/"
                )]
            )
        )

        glue.CfnCrawler(
            self,
            "AuctionKeyValueLabelFeedCrawler",
            name="auction_kv_labels_feed",
            role=glue_crawler_role.role_name,
            database_name=log_level_data_database,
            targets=glue.CfnCrawler.TargetsProperty(
                s3_targets=[glue.CfnCrawler.S3TargetProperty(
                    path=f"s3://{log_level_data_bucket.bucket_name}/feeds/auction_kv_labels_feed/"
                )]
            )
        )

    def prepare_partition_query(self):
        return sfn.Pass(
            self,
            "Prepare 'Create partition query'",
            parameters={
                "QueryString":
                    sfn.JsonPath.format(
                        'ALTER TABLE {} ADD IF NOT EXISTS '
                        '   PARTITION ('
                        '       partition_0 = \"{}\", '
                        '       partition_1 = \"{}\", '
                        '       partition_2 = \"{}\", '
                        '       partition_3 = \"{}\", '
                        '       partition_4 = \"{}\") '
                        '       LOCATION \"s3://{}{}/\"',
                        sfn.JsonPath.string_at(json_value_selectors.TABLE_NAME),
                        sfn.JsonPath.string_at(json_value_selectors.YEAR_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.MONTH_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.DAY_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.HOUR_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.PROCESSING_TIME),
                        sfn.JsonPath.string_at(json_value_selectors.BUCKET_NAME),
                        sfn.JsonPath.string_at(json_value_selectors.S3_PATH)
                    )
            },
            result_path="$.query"
        )

    def run_athena_create_partition_query(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Run Athena Query 'Create partition query'",
            query_string=sfn.JsonPath.string_at("$.query.QueryString"),
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def ensure_temporary_parquet_table_standard_feed_exists(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Ensure Temporary Parquet Table Standard Feed Exists",
            query_string="CREATE EXTERNAL TABLE IF NOT EXISTS `standard_feed_parquet_temp` ("
                         "  `auction_id_64` bigint, "
                         "  `site_id` int, "
                         "  `tag_id` int, "
                         "  `imp_type` int, "
                         "  `view_result` int, "
                         "  `revenue` double, "
                         "  `creative_width` int, "
                         "  `creative_height` int, "
                         "  `publisher_id` int, "
                         "  `device_type` int, "
                         "  `line_item_id` int, "
                         "  `user_id_64` bigint, "
                         "  `advertiser_id` int, "
                         "  `buyer_member_id` int, "
                         "  `deal_id` int "
                         ") PARTITIONED BY ("
                         "  `partition_0` string, "
                         "  `partition_1` string, "
                         "  `partition_2` string, "
                         "  `partition_3` string"
                         ") ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' "
                         "STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' "
                         "OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat' "
                         f"LOCATION 's3://{self.processing_bucket.bucket_name}/standard_feed_parquet_temp' "
                         "TBLPROPERTIES ('parquet.compression'='ZSTD', 'compression_level' = '22')",
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def ensure_temporary_parquet_table_key_value_feed_exists(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Ensure Temporary Parquet Table Key Value Feed Exists",
            query_string="CREATE EXTERNAL TABLE IF NOT EXISTS `auction_kv_labels_feed_parquet_temp` ("
                         "  `auction_id_64` bigint, "
                         "  `key_values` map<string,array<string>>"
                         ") PARTITIONED BY ("
                         "  `partition_0` string, "
                         "  `partition_1` string, "
                         "  `partition_2` string, "
                         "  `partition_3` string"
                         ") ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' "
                         "STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' "
                         "OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat' "
                         f"LOCATION 's3://{self.processing_bucket.bucket_name}/auction_kv_labels_feed_parquet_temp' "
                         "TBLPROPERTIES ('parquet.compression'='ZSTD', 'compression_level' = '22', 'partition_filtering.enabled' = 'true')",
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def ensure_temporary_parquet_table_clicks_exists(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Ensure Temporary Parquet Table Clicks Exists",
            query_string="CREATE EXTERNAL TABLE IF NOT EXISTS `clicks_parquet_temp` ("
                         "  `auction_id_64` bigint "
                         ") PARTITIONED BY ("
                         "  `partition_0` string, "
                         "  `partition_1` string, "
                         "  `partition_2` string, "
                         "  `partition_3` string"
                         ") ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' "
                         "STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' "
                         "OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat' "
                         f"LOCATION 's3://{self.processing_bucket.bucket_name}/clicks_parquet_temp' "
                         "TBLPROPERTIES ('parquet.compression'='ZSTD', 'compression_level' = '22')",
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def ensure_multi_key_value_report_table_exists(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Ensure Multi Key Value Report Table Exists",
            query_string=f"CREATE EXTERNAL TABLE IF NOT EXISTS `{TABLE_MULTI_KEY_VALUE_MICROSOFT}` ("
                         "  `auction_id_64` bigint, "
                         "  `publisher_id` int, "
                         "  `site_id` int, "
                         "  `tag_id` int, "
                         "  `imp_type` int, "
                         "  `device_type` int, "
                         "  `creative_size` string, "
                         "  `imps` int, "
                         "  `imps_viewed` int, "
                         "  `imps_view_measured` int, "
                         "  `revenue` double, "
                         "  `key_values` map<string,array<string>>, "
                         "  `line_item_id` int, "
                         "  `user_id_64` bigint, "
                         "  `clicks` int "
                         ") PARTITIONED BY ("
                         "  `date` string, "
                         "  `hour` string "
                         ") ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' "
                         "STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' "
                         "OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat' "
                         f"LOCATION 's3://{self.processing_bucket.bucket_name}/{TABLE_MULTI_KEY_VALUE_MICROSOFT}' "
                         "TBLPROPERTIES ('parquet.compression'='ZSTD', 'compression_level' = '22')",
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def query_for_unprocessed_partitions(self, source_table_name, destination_table_name):
        return tasks.AthenaStartQueryExecution(
            self,
            f"Query Unprocessed Partitions In {destination_table_name}",
            query_string="SELECT DISTINCT source.partition_0, "
                         "  source.partition_1, "
                         "  source.partition_2, "
                         "  source.partition_3 "
                         f"FROM \"{source_table_name}$partitions\" source "
                         f"  LEFT OUTER JOIN \"{destination_table_name}$partitions\" destination "
                         "  ON source.partition_0 = destination.partition_0 "
                         "      AND source.partition_1 = destination.partition_1 "
                         "      AND source.partition_2 = destination.partition_2 "
                         "      AND source.partition_3 = destination.partition_3 "
                         "WHERE "
                         "  destination.partition_0 is null "
                         "  AND date(concat_ws('-', source.partition_0, source.partition_1, source.partition_2) ) "
                         "> (CURRENT_DATE - INTERVAL '7' DAY)",
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def read_results_step(self, feed_name, prepare_conversion_query_lambda):
        read_results_step = sfn.DistributedMap(
            self,
            f"Read Results Step {feed_name}",
            item_reader=sfn.S3CsvItemReader(
                bucket=self.athena_results_bucket,
                key=sfn.JsonPath.format(
                    "mkvr/{}.csv", sfn.JsonPath.string_at("$.output.QueryExecution.QueryExecutionId")
                ),
                max_items=100
            )
        ).add_catch(sfn.Succeed(self, f"Results File For {feed_name} Is Empty"))

        read_results_step.item_processor(
            tasks.LambdaInvoke(
                self,
                f"Create Conversion Query {feed_name}",
                lambda_function=prepare_conversion_query_lambda,
                output_path=json_value_selectors.PAYLOAD
            )
        )

        return read_results_step

    def run_queries(self, table_name):
        queries_loop = sfn.Map(
            self,
            f"Query {table_name}",
            max_concurrency=5,
            result_path=sfn.JsonPath.DISCARD,
            output_path=sfn.JsonPath.DISCARD
        )

        queries_loop.item_processor(
            tasks.AthenaStartQueryExecution(
                self,
                f"Process Query For {table_name}",
                query_string=sfn.JsonPath.string_at("$.query"),
                query_execution_context=self.query_execution_context,
                work_group=self.athena_work_group.name,
                integration_pattern=sfn.IntegrationPattern.RUN_JOB
            )
        )

        return queries_loop

    def query_processable_partitions(self):
        return tasks.AthenaStartQueryExecution(
            self,
            "Query For Processable Partitions",
            query_string="WITH available_interval AS ("
                         "SELECT greatest(min(date_and_time), date_add('day', -4, current_timestamp)) AS min_join_datetime, "
                         "  max(date_and_time) AS max_join_datetime "
                         "FROM (SELECT date_parse(concat_ws(' ', concat_ws('-', s.partition_0, s.partition_1, s.partition_2),s.partition_3), '%Y-%m-%d %H') AS date_and_time "
                         "      FROM \"auction_kv_labels_feed_parquet_temp$partitions\" s "
                         "      ) "
                         "), "
                         "processable_partitions AS ( "
                         "  SELECT "
                         "      DISTINCT s.partition_0, s.partition_1, s.partition_2, s.partition_3 "
                         "  FROM \"standard_feed_parquet_temp$partitions\" s, "
                         "  available_interval a, "
                         "  \"clicks_parquet_temp$partitions\" c "
                         "  WHERE s.partition_0 = c.partition_0 AND s.partition_1 = c.partition_1 AND s.partition_2 = c.partition_2 AND s.partition_3 = c.partition_3 "
                         "      AND date_parse(concat_ws(' ', concat_ws('-', s.partition_0, s.partition_1, s.partition_2), s.partition_3), '%Y-%m-%d %H') > a.min_join_datetime "
                         "      AND date_parse(concat_ws(' ', concat_ws('-', s.partition_0, s.partition_1, s.partition_2), s.partition_3), '%Y-%m-%d %H') < a.max_join_datetime "
                         ") "
                         "SELECT "
                         "  p.partition_0, p.partition_1, p.partition_2, p.partition_3 "
                         "FROM processable_partitions p "
                         f"LEFT OUTER JOIN \"{TABLE_MULTI_KEY_VALUE_MICROSOFT}$partitions\" k "
                         "  ON concat_ws('-', p.partition_0, p.partition_1, p.partition_2) = k.date AND p.partition_3 = k.hour "
                         "WHERE k.date IS NULL"
            ,
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )
