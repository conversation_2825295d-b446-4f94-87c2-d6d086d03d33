import uuid
from os import path
from typing import List

from strictyaml import load

from aws_cdk import (
    Duration,
    aws_s3 as s3,
    aws_lambda as lambda_,
    aws_stepfunctions as sfn,
    aws_stepfunctions_tasks as tasks,
    aws_iam as iam,
    aws_certificatemanager as acm,
    aws_ssm as ssm,
    aws_route53 as route53,
    aws_route53_targets as targets,
    aws_apigatewayv2 as apigwv2,
    aws_apigatewayv2_integrations as integrations,
    aws_scheduler as scheduler,
    aws_s3_deployment as s3deploy,
    aws_ec2 as ec2,
    BundlingOptions, CfnDynamicReference, CfnDynamicReferenceService
)
from constructs import Construct

from insighter_commons import json_value_selectors, REPORT_DEFINITION_SCHEMA
from insighter_commons.constants import (
    DEFAULT_LAMBDA_HANDLER,
    TABLE_MULTI_KEY_VALUE_GOOGLE,
    TABLE_MULTI_KEY_VALUE_MICROSOFT,
    get_report_definition_file_paths
)


class ReportProcessing(Construct):
    def __init__(
            self,
            scope: Construct,
            construct_id: str,
            root_dir: str,
            domain_name: str,
            processing_bucket: s3.IBucket,
            report_configurations_prefix: str
    ):
        super().__init__(scope, construct_id)

        self.processing_bucket = processing_bucket
        self.domain_name = domain_name
        self.configurations_prefix = report_configurations_prefix
        self.root_dir = root_dir

        # Report definitions
        exclude_paths = self.get_expired_report_paths(root_dir=root_dir)
        s3deploy.BucketDeployment(
            self, "reporting-definitions",
            sources=[s3deploy.Source.asset(
                path=path.join(root_dir, "multi_key_value_reports", "report_definitions"),
                exclude=exclude_paths)
            ],
            destination_bucket=self.processing_bucket,
            destination_key_prefix="report_definitions"
        )

        self.grant_cross_account_read_permissions()

        list_report_configurations_lambda = lambda_.Function(
            self,
            "ListReportConfigurations",
            function_name="insighter-list-report-configurations",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/reporting/list_report_configurations"
            ),
            environment={
                "PROCESSING_BUCKET_NAME": processing_bucket.bucket_name,
                "CONFIGURATIONS_PREFIX": report_configurations_prefix,
            },
            timeout=Duration.seconds(60)
        )

        workmail_credentials = ssm.StringParameter.from_secure_string_parameter_attributes(
            self,
            "WorkmailCredentials",
            parameter_name='/workmail/noreply'
        )

        prepare_and_send_email_lambda = lambda_.Function(
            self,
            "PrepareAndSendEmailLambda",
            function_name="insighter-prepare-and-send-email",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/reporting/prepare_and_send_email",
                bundling=BundlingOptions(
                    image=lambda_.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install --platform manylinux2014_x86_64 --only-binary=:all: --no-cache -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            ),
            environment={
                "HOME": "/tmp",
                "PROCESSING_BUCKET_NAME": processing_bucket.bucket_name,
                "DOMAIN_NAME": domain_name,
                "smtp_host": "smtp.mail.eu-west-1.awsapps.com",
                "smtp_port": "465"
            },
            memory_size=2048,
            timeout=Duration.seconds(600)
        )

        workmail_credentials.grant_read(prepare_and_send_email_lambda)

        inisghter_commons_dynamic_ref = CfnDynamicReference(
            CfnDynamicReferenceService.SSM,
            "/insighter/commons_layer_arn"
        )
        register_processing_tasks_lambda = lambda_.Function(
            self,
            "RegisterProcessingTasksFunction",
            function_name="insighter-register-processing-tasks",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            layers=[lambda_.LayerVersion.from_layer_version_arn(
                self,
                "InsighterCommonsLayerVersion",
                inisghter_commons_dynamic_ref.to_string()
            )],
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/reporting/register_processing_tasks"
            ),
            environment={
                "PROCESSING_BUCKET_NAME": processing_bucket.bucket_name,
                "taskRoleArn": ssm.StringParameter.value_from_lookup(self, "/insighter/task_role_arn"),
                "executionRoleArn": ssm.StringParameter.value_from_lookup(self, "/insighter/task_execution_role_arn"),
                "log_group_name": ssm.StringParameter.value_from_lookup(self, "/insighter/log_group_name"),
                "TABLE_MULTI_KEY_VALUE_GOOGLE": TABLE_MULTI_KEY_VALUE_GOOGLE,
                "TABLE_MULTI_KEY_VALUE_MICROSOFT": TABLE_MULTI_KEY_VALUE_MICROSOFT
            },
            timeout=Duration.seconds(180)
        )

        register_processing_tasks_lambda.role.add_managed_policy(
            iam.ManagedPolicy.from_aws_managed_policy_name("AmazonECS_FullAccess")
        )

        cleanup_processing_tasks_lambda = lambda_.Function(
            self,
            "CleanupReportConfigurationsFunction",
            function_name="insighter-cleanup-report-configurations",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/reporting/cleanup_report_configurations"
            ),
            environment={
                "PROCESSING_BUCKET_NAME": processing_bucket.bucket_name,
                "CONFIGURATIONS_PREFIX": report_configurations_prefix,
            },
            timeout=Duration.seconds(60)
        )

        processing_bucket.grant_read(list_report_configurations_lambda)
        processing_bucket.grant_read_write(register_processing_tasks_lambda)
        processing_bucket.grant_read_write(prepare_and_send_email_lambda)
        processing_bucket.grant_read_write(cleanup_processing_tasks_lambda)

        reporting_definition = self.list_report_configurations(list_report_configurations_lambda).next(
            self.read_report_configurations(register_processing_tasks_lambda).next(
                self.process_tasks().next(
                    self.send_email_step(prepare_and_send_email_lambda).next(
                        self.cleanup_report_configurations(cleanup_processing_tasks_lambda)
                    )
                )
            )
        )

        reporting_step_function = sfn.StateMachine(
            self,
            "Duckdb",
            state_machine_name="Insighter-Report-Creation",
            definition_body=sfn.DefinitionBody.from_chainable(reporting_definition)
        )

        reporting_step_function.role.add_managed_policy(
            iam.ManagedPolicy.from_aws_managed_policy_name("AmazonECS_FullAccess")
        )

        scheduler_role = iam.Role(
            self,
            "InsighterSchedulerRole",
            assumed_by=iam.ServicePrincipal("scheduler.amazonaws.com")
        )

        scheduler_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "states:StartExecution",
                    "s3:ListBucket",
                    "s3:GetBucketLocation"
                ],
                resources=[
                    reporting_step_function.state_machine_arn
                ]
            )
        )

        scheduler.CfnSchedule(
            self,
            "ReportSchedule",
            name="InsighterReportSchedule",
            schedule_expression="cron(0 8 * * ? *)",
            schedule_expression_timezone="Europe/Berlin",
            flexible_time_window=scheduler.CfnSchedule.FlexibleTimeWindowProperty(
                mode="OFF"
            ),
            target=scheduler.CfnSchedule.TargetProperty(
                arn=reporting_step_function.state_machine_arn,
                role_arn=scheduler_role.role_arn
            )
        )

        processing_bucket.grant_read(reporting_step_function)

        oauth_client_credentials = ssm.StringParameter.from_secure_string_parameter_attributes(
            self,
            "OAuthClientCredentials",
            parameter_name='/insighter/aws-lambda-keycloak-client-configuration'
        )

        web_adapter_layer = lambda_.LayerVersion.from_layer_version_arn(
            self,
            "WebAdapter",
            "arn:aws:lambda:eu-central-1:************:layer:LambdaAdapterLayerX86:22"
        )

        api_download_lambda = lambda_.Function(
            self,
            "DownloadReportFunction",
            function_name="insighter-download-report-function",
            runtime=lambda_.Runtime.PYTHON_3_12,
            handler="run.sh",
            layers=[web_adapter_layer],
            environment={
                "SESSION_SECRET_KEY": str(uuid.uuid4()),
                "OAUTH_PARAMETER_NAME": oauth_client_credentials.parameter_name,
                "AWS_LAMBDA_EXEC_WRAPPER": "/opt/bootstrap",
                "PORT": "8000"
            },
            code=lambda_.Code.from_asset(
                "multi_key_value_reports/functions/reporting/download_report",
                bundling=BundlingOptions(
                    image=lambda_.Runtime.PYTHON_3_12.bundling_image,
                    command=[
                        "bash", "-c",
                        "pip install --platform manylinux2014_x86_64 --only-binary=:all: --no-cache -r requirements.txt -t /asset-output && cp -au . /asset-output"
                    ],
                )
            ),
            timeout=Duration.seconds(60)
        )

        oauth_client_credentials.grant_read(api_download_lambda)
        processing_bucket.grant_read(api_download_lambda)

        hosted_zone = route53.HostedZone.from_lookup(
            self,
            "HostedZone",
            domain_name=domain_name
        )

        lambda_integration = integrations.HttpLambdaIntegration("APILambdaIntegration", api_download_lambda)

        cert_arn = "arn:aws:acm:eu-central-1:083168087794:certificate/b1dfd541-2f3f-45a4-ba13-4e2f20d3b757"
        domain_name = apigwv2.DomainName(
            self,
            "DomainName",
            domain_name=domain_name,
            certificate=acm.Certificate.from_certificate_arn(self, "cert", cert_arn),
            endpoint_type=apigwv2.EndpointType.REGIONAL
        )

        apigwv2.HttpApi(
            self,
            "HttpProxyApi",
            default_integration=lambda_integration,
            default_domain_mapping=apigwv2.DomainMappingOptions(
                domain_name=domain_name
            )
        )

        route53.ARecord(
            self,
            "AliasRecord",
            zone=hosted_zone,
            target=route53.RecordTarget.from_alias(
                targets.ApiGatewayv2DomainProperties(
                    domain_name.regional_domain_name,
                    domain_name.regional_hosted_zone_id
                )
            )
        )

    def list_report_configurations(self, list_report_configurations_lambda):
        return tasks.LambdaInvoke(
            self,
            "List Report Configurations",
            lambda_function=list_report_configurations_lambda,
            output_path=json_value_selectors.PAYLOAD
        )

    def read_report_configurations(self, register_processing_tasks_lambda):
        read_configurations_step = sfn.DistributedMap(
            self,
            "Read Report Configurations",
            items_path=json_value_selectors.REPORTS_ATTRIBUTE,
            result_path=json_value_selectors.REPORTS_ATTRIBUTE
        )

        read_configurations_step.item_processor(
            tasks.LambdaInvoke(
                self,
                "Register Processing Task Definitions",
                lambda_function=register_processing_tasks_lambda,
                output_path=json_value_selectors.PAYLOAD
            )
        )

        return read_configurations_step

    def process_tasks(self):
        reports_map = sfn.Map(
            self,
            "Loop Reports",
            items_path=sfn.JsonPath.string_at(json_value_selectors.REPORTS_ATTRIBUTE),
            result_path=sfn.JsonPath.DISCARD
        )

        task_map = sfn.Map(
            self,
            "Loop Tasks",
            items_path=sfn.JsonPath.string_at("$.task_definitions"),
            item_selector={
                "task_definition": sfn.JsonPath.string_at("$$.Map.Item.Value")
            }
        )

        vpc = ec2.Vpc.from_lookup(
            self, "analytics-vpc",
            vpc_id=ssm.StringParameter.value_from_lookup(
                self,
                '/analytics/vpc_id'
            )
        )
        subnet_ids = []
        for subnet in vpc.private_subnets:
            subnet_ids.append(subnet.subnet_id)

        task_map.item_processor(
            sfn.CustomState(
                self,
                "Run ECS Task",
                state_json={
                    "Type": "Task",
                    "Resource": "arn:aws:states:::ecs:runTask.sync",
                    "Parameters": {
                        "LaunchType": "FARGATE",
                        "Cluster": ssm.StringParameter.value_from_lookup(self, "/insighter/ecs_cluster_arn"),
                        "TaskDefinition.$": "$.task_definition",
                        "PropagateTags": "TASK_DEFINITION",
                        "NetworkConfiguration": {
                            "AwsvpcConfiguration": {
                                "SecurityGroups": [ssm.StringParameter.value_from_lookup(self, "/insighter/ecs_security_group_id")],
                                "Subnets": subnet_ids
                            }
                        }
                    }
                }
            ).next(
                sfn.CustomState(
                    self,
                    "Deregister ECS Task",
                    state_json={
                        "Type": "Task",
                        "Parameters": {
                            "TaskDefinition.$": "$.TaskDefinitionArn",
                        },
                        "Resource": "arn:aws:states:::aws-sdk:ecs:deregisterTaskDefinition"
                    }
                ).next(
                    sfn.CustomState(
                        self,
                        "Delete ECS Task Definitions",
                        state_json={
                            "Type": "Task",
                            "Parameters": {
                                "TaskDefinitions.$": "States.Array($.TaskDefinition.TaskDefinitionArn)"
                            },
                            "Resource": "arn:aws:states:::aws-sdk:ecs:deleteTaskDefinitions"
                        }
                    )
                )
            )
        )

        reports_map.item_processor(task_map)

        return reports_map

    def send_email_step(self, prepare_email_lambda):
        return tasks.LambdaInvoke(
            self,
            "Send Email",
            lambda_function=prepare_email_lambda,
            output_path=json_value_selectors.PAYLOAD
        )

    def cleanup_report_configurations(self, cleanup_report_configurations_lambda):
        return tasks.LambdaInvoke(
            self,
            "Cleanup Report Configurations",
            lambda_function=cleanup_report_configurations_lambda
        )

    @staticmethod
    def get_expired_report_paths(root_dir: str) -> List[str]:
        from datetime import datetime

        expired_report_paths = []
        for file_path in get_report_definition_file_paths(root_dir=root_dir):
            with (open(file_path, "r") as report_file):
                config = load(report_file.read(), REPORT_DEFINITION_SCHEMA).data
                if 'end_date' in config and config['end_date']:
                    if config['end_date'].date() < datetime.now().date():
                        expired_report_paths.append("**/" + file_path.split("/")[-1])
        return expired_report_paths

    def grant_cross_account_read_permissions(self):
        statements = []
        for file_path in get_report_definition_file_paths(root_dir=self.root_dir):
            with open(file_path, "r") as report_file:
                config = load(report_file.read(), REPORT_DEFINITION_SCHEMA).data
                report_id = file_path.split("/")[-1].split(".")[0]
                if 's3_sharing_account_id' in config:
                    statements.extend(
                        [
                            iam.PolicyStatement(
                                actions=[
                                    's3:ListBucket'
                                ],
                                effect=iam.Effect.ALLOW,
                                principals=[
                                    iam.AccountPrincipal(config['s3_sharing_account_id'])
                                ],
                                resources=[f"{self.processing_bucket.bucket_arn}"],
                                conditions={
                                    "StringLike": {
                                        "s3:prefix": f"downloads/{report_id}/*"
                                    }
                                }
                            ),
                            iam.PolicyStatement(
                                actions=[
                                    's3:Get*'
                                ],
                                effect=iam.Effect.ALLOW,
                                principals=[
                                    iam.AccountPrincipal(config['s3_sharing_account_id'])
                                ],
                                resources=[f"{self.processing_bucket.arn_for_objects(f"downloads/{report_id}/*")}"]
                            )
                        ]
                    )

        if len(statements) > 0:
            s3.CfnBucketPolicy(
                self, "BucketPolicy",
                bucket=self.processing_bucket.bucket_name,
                policy_document=iam.PolicyDocument(
                    statements=statements,
                )
            )
