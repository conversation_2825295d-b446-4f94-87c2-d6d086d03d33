name: <PERSON><PERSON><PERSON> - Missing Placements
start_date: 2024-11-01
end_date:
report_timespan: LAST_DAY
data_sources:
  - MICROSOFT
s3_sharing_account_id: ************
notification_recipients:
  - <EMAIL>
  - <EMAIL>
sql: >
  WITH filtered AS (SELECT *, 
                        placement.id AS placement_id,
                        unnest(key_values['contid']) AS contid,
                        unnest(key_values['gpid']) AS gpid, 
                        unnest(key_values['content_url']) AS content_url
                    FROM read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**')
                    JOIN read_parquet('s3://business-integration-data-lake/data/raw/an/placements/**.gz.parquet') placement
                        ON placement.code LIKE '%_default_placement%'
                            AND placement.id = tag_id
                    WHERE list_contains(map_keys(key_values), 'content_url')
                )
  SELECT
    date AS dd, 
    placement_id, 
    CASE 
      WHEN gpid IS NOT NULL THEN gpid
      ELSE contid 
    END AS placement,
    content_url, 
    SUM(imps) AS impressions
  FROM filtered
  GROUP BY date, contid, gpid, placement_id, content_url
  ORDER BY date ASC, impressions DESC
ecs_memory: 32768
