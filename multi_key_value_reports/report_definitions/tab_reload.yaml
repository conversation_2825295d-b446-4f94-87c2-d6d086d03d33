name: Tab Reload Branch Performance Comparison
start_date: 2024-08-15
end_date: 2024-08-31
report_timespan: LAST_DAY
data_sources:
  - MICROSOFT
notification_recipients:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
sql: >
  WITH filtered AS (SELECT *, 
  unnest(key_values['contid']) AS contid, 
  unnest(key_values['reloadtrigger']) AS reloadtrigger,
  unnest(key_values['browser']) AS browser,
  list_filter(key_values['branch'], branch -> list_contains(['abtest', 'master', 'development', 'alpha'], branch)) AS branch 
  FROM read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**') 
  WHERE list_contains(map_keys(key_values), 'reloadtrigger') 
  AND list_contains(map_keys(key_values), 'branch')) 
  SELECT 
    date, 
    reloadtrigger, 
    branch[1] AS branch, 
    browser,
    contid, 
    CASE WHEN imp_type=1 THEN 'Blank' 
      WHEN imp_type=2 THEN 'PSA' 
      WHEN imp_type=3 THEN 'Default Error' 
      WHEN imp_type=4 THEN 'Default' 
      WHEN imp_type=5 THEN 'Kept' 
      WHEN imp_type=6 THEN 'Resold' 
      WHEN imp_type=7 THEN 'RTB' 
      WHEN imp_type=8 THEN 'PSA Error' 
      WHEN imp_type=9 THEN 'External Impression' 
      WHEN imp_type=10 THEN 'External Click' 
      WHEN imp_type=11 THEN 'Insertion' 
    END AS impression_type,
    CASE WHEN device_type=0 THEN 'Other Devices'
      WHEN device_type=1 THEN 'Desktops and Laptops'
      WHEN device_type=2 THEN 'Mobile Phones'
      WHEN device_type=3 THEN 'Tablets'
      WHEN device_type=4 THEN 'TV'
      WHEN device_type=5 THEN 'Game Console'
      WHEN device_type=6 THEN 'Media Players'
      WHEN device_type=7 THEN 'Set Top Box'
    END AS device_type,
    publisher_id,
    sum(imps) AS impressions, 
    sum(imps_view_measured) AS imps_view_measured, 
    sum(imps_viewed) AS viewed_impressions, 
    cast(sum(revenue) as DECIMAL(38, 20)) AS revenue 
    FROM filtered 
  WHERE reloadtrigger = 'activetab' AND branch[1] IS NOT NULL 
  GROUP BY date, reloadtrigger, branch, contid, imp_type, device_type, publisher_id, browser
  ORDER BY date, reloadtrigger, branch, contid, imp_type, device_type, publisher_id, browser
ecs_memory: 32768
