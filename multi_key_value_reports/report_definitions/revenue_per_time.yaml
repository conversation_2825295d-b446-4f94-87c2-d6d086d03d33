name: Revenue Per Time
start_date: 2025-05-01
end_date:
report_timespan: LAST_MONTH
data_sources:
  - MICROSOFT
  - GOOGLE
notification_recipients:
  - <EMAIL>
sql: >
    WITH microsoft AS (
        SELECT
            date,
            auction_id_64,
            imps,
            imps_viewed,
            revenue as m_revenue,
            unnest(key_values['hasvideoplayer'])                           as hasvideoplayer,
            list_filter(key_values['misc'], misc -> list_contains(['vidpage'], misc))[1] as misc,
        FROM read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**')
        WHERE list_contains(map_keys(key_values), 'hasvideoplayer')
            AND publisher_id = 1000494
    ), google AS (
        SELECT 
            CAST(auction_id AS BIGINT) as auction_id, 
            revenue as g_revenue,
        FROM read_parquet([
            's3://{bucket_name}/{multi_key_value_google}/partition_0=' || strftime('%Y', CAST('{date}' AS DATE)) || '/partition_1=' || strftime('%m', CAST('{date}' AS DATE)) || '/partition_2=' || strftime('%d', CAST('{date}' AS DATE)) || '/**',
            's3://{bucket_name}/{multi_key_value_google}/partition_0=' || strftime('%Y', CAST('{date}' AS DATE) - INTERVAL 1 DAY) || '/partition_1=' || strftime('%m', CAST('{date}' AS DATE) - INTERVAL 1 DAY) || '/partition_2=' || strftime('%d', CAST('{date}' AS DATE) - INTERVAL 1 DAY) || '/**'
        ])
        WHERE auction_id IS NOT NULL 
            AND (publisher = 'bild.de' OR publisher IS NULL) 
    ), as_dimensions AS (
        SELECT 
            m_auction_id,
            as_impression_type,
            as_supply_type,
            as_supply_path,
            as_market_type,
            as_ssp,
            as_page_type,
            g_partition_0
        FROM read_parquet('s3://{bucket_name}/as_dimensions/date={date}/**')
        WHERE as_supply_type = 'm_web' OR as_supply_type = 'web'
    ), combined AS (
        SELECT
            date,
            auction_id_64 as m_auction_id,
            hasvideoplayer,
            misc, 
            m.imps as m_impressions, 
            m.imps_viewed as m_viewed_impressions,
            m_revenue,
            g_revenue
        FROM microsoft m
        LEFT JOIN google g
        ON m.auction_id_64 = g.auction_id
    ), enriched AS (
        SELECT 
            date,
            CASE 
                WHEN as_supply_path LIKE 'gam_%' THEN 'GOOGLE'
                WHEN as_impression_type LIKE 'programmatic (gam)' AND as_supply_path = 'unknown' AND g_partition_0 IS NOT NULL THEN 'GOOGLE'
                WHEN as_impression_type LIKE 'programmatic (gam)' AND as_supply_path = 'unknown' AND g_partition_0 IS NULL THEN 'unknown'
                ELSE 'MICROSOFT'
            END as origin,
            hasvideoplayer,
            misc,
            m_impressions,
            m_viewed_impressions,
            CASE 
                WHEN as_supply_path LIKE 'gam_%' THEN g_revenue
                ELSE m_revenue
            END as revenue,
            as_impression_type,
            as_supply_type,
            as_supply_path,
            as_market_type,
            as_ssp,
            as_page_type
        FROM combined
        LEFT JOIN as_dimensions
        ON combined.m_auction_id = as_dimensions.m_auction_id
    )
    SELECT 
        date,
        origin, 
        hasvideoplayer as has_video_player, 
        misc, 
        as_impression_type,
        as_supply_type,
        as_supply_path,
        as_market_type,
        as_ssp,
        as_page_type,
        sum(revenue) as revenue,
        sum(m_impressions) as impressions,
        sum(m_viewed_impressions) as viewed_impressions
    FROM enriched
    WHERE as_supply_type = 'm_web' OR as_supply_type = 'web'
    GROUP BY
        date,
        origin,
        hasvideoplayer, 
        misc, 
        as_impression_type,
        as_supply_type,
        as_supply_path,
        as_market_type,
        as_ssp,
        as_page_type
ecs_memory: 122880
