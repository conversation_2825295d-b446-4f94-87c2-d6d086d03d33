name: <PERSON> Performance Comparison
start_date: 2024-05-03
end_date:
report_timespan: REPORTING_LIFETIME
data_sources:
  - MICROSOFT
notification_recipients:
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
  - <EMAIL>
sql: >
  WITH filtered AS (SELECT *,
                           unnest(key_values['reload'])                                                                                   as reload,
                           list_filter(key_values['branch'], branch ->
                                                                     list_contains(['abtest', 'master', 'development', 'alpha'], branch)) as branch
                    FROM read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**')
                    WHERE list_contains(map_keys(key_values), 'branch')
                      AND list_contains(map_keys(key_values), 'reload'))
  SELECT
      date, reload, branch[1] AS branch, sum(imps) as impressions, sum(imps_viewed) as viewed_impressions, sum(revenue) as revenue
  FROM filtered
  GROUP BY date, branch, reload
  ORDER BY date, branch, reload
ecs_memory: 32768
