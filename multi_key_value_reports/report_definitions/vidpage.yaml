name: <PERSON> Detail Page Performance
start_date: 2024-06-01
end_date:
report_timespan: REPORTING_LIFETIME
data_sources:
  - MICROSOFT
notification_recipients:
  - <EMAIL>
  - jakob.sto<PERSON><PERSON>@axelspringer.com
  - <EMAIL>
  - <EMAIL>
sql: >
  WITH filtered AS (SELECT *,
                           unnest(key_values['reload']) as reload,
                           list_filter(key_values['misc'], misc -> list_contains(['vidpage'], misc)) as misc
                    FROM read_parquet('s3://{bucket_name}/{multi_key_value_microsoft}/date={date}/**')
                    WHERE list_contains(map_keys(key_values), 'misc')
                      AND list_contains(map_keys(key_values), 'reload'))
  SELECT
      date, reload, misc[1] AS misc, sum(imps) as impressions, sum(imps_viewed) as viewed_impressions, sum(revenue) as revenue
  FROM filtered
  WHERE misc[1] IS NOT NULL
  GROUP BY date, misc[1], reload
  ORDER BY date, misc[1], reload
ecs_memory: 32768
