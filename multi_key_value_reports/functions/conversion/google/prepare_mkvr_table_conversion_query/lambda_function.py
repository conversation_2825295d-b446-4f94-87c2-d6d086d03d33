import os


def lambda_handler(event, context):
    sql = (
        f"INSERT INTO {os.environ.get("TABLE_MULTI_KEY_VALUE_GOOGLE")} "
        "( "
        "  auction_id, "
        "  line_item_id, "
        "  publisher, "
        "  measurable_view, "
        "  viewable_view, "
        "  eligible_view, "
        "  click, "
        "  impression, "
        "  revenue, "
        "  year, "
        "  month, "
        "  day, "
        "  hour, "
        "  partition_0, "
        "  partition_1, "
        "  partition_2, "
        "  partition_3 "
        ") "
        "  SELECT "
        "    auction_id, "
        "    line_item_id, "
        "    regexp_replace(element_at(transform(parentPath, x -> x.name), 2), '-mew|_mediathek|(test|bundesliga)?[0-9]?_|-desktop', '') as publisher, "
        "    result_measurable_view AS measurable_view, "
        "    viewable_view, "
        "    eligible_view, "
        "    click, "
        "    impression, "
        "    revenue, "
        "    year, "
        "    month, "
        "    day, "
        "    hour, "
        "    partition_0, "
        "    partition_1, "
        "    partition_2, "
        "    partition_3 "
        "  FROM ( "
        "    SELECT "
        "      ni.auction_id, "
        "      ni.line_item_id, "
        "      ni.ad_unit_id, "
        "      (ni.measurable_view + na.measurable_view) AS result_measurable_view, "
        "      na.viewable_view, "
        "      ni.eligible_view, "
        "      nc.click, "
        "      ni.impression, "
        "      ni.revenue, "
        "      ni.year, "
        "      ni.month, "
        "      ni.day, "
        "      ni.hour, "
        "      ni.partition_0, "
        "      ni.partition_1, "
        "      ni.partition_2, "
        "      ni.partition_3 "
        "    FROM "
        f"   {os.environ.get("TEMP_TABLE_NETWORK_IMPRESSIONS")} ni "
        "    LEFT JOIN "
        f"   {os.environ.get("TEMP_TABLE_NETWORK_ACTIVE_VIEWS")} na "
        "    ON "
        "    ni.auction_id = na.auction_id "
        "    AND ni.partition_0 = na.partition_0 "
        "    AND ni.partition_1 = na.partition_1 "
        "    AND ni.partition_2 = na.partition_2 "
        "    AND ni.partition_3 = na.partition_3 "
        "    LEFT JOIN "
        f"   {os.environ.get("TEMP_TABLE_NETWORK_CLICKS")} nc "
        "    ON "
        "    ni.auction_id = nc.auction_id "
        "    AND ni.partition_0 = nc.partition_0 "
        "    AND ni.partition_1 = nc.partition_1 "
        "    AND ni.partition_2 = nc.partition_2 "
        "    AND ni.partition_3 = nc.partition_3 "
        "    WHERE "
        f"     ni.partition_0 = '{event['partition_0']}' "
        f"     AND ni.partition_1 = '{event['partition_1']}' "
        f"     AND ni.partition_2 = '{event['partition_2']}' "
        f"     AND ni.partition_3 = '{event['partition_3']}' "
        "    UNION "
        "    SELECT "
        "      nbi.auction_id, "
        "      nbi.line_item_id, "
        "      nbi.ad_unit_id, "
        "      (nbi.measurable_view + nba.measurable_view) AS result_measurable_view, "
        "      nba.viewable_view, "
        "      nbi.eligible_view, "
        "      nbc.click, "
        "      nbi.impression, "
        "      nbi.revenue, "
        "      nbi.year, "
        "      nbi.month, "
        "      nbi.day, "
        "      nbi.hour, "
        "      nbi.partition_0, "
        "      nbi.partition_1, "
        "      nbi.partition_2, "
        "      nbi.partition_3 "
        "    FROM "
        f"   {os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS")} nbi "
        "    LEFT JOIN "
        f"   {os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS")} nba "
        "    ON "
        "    nbi.auction_id = nba.auction_id "
        "    AND nbi.partition_0 = nba.partition_0 "
        "    AND nbi.partition_1 = nba.partition_1 "
        "    AND nbi.partition_2 = nba.partition_2 "
        "    AND nbi.partition_3 = nba.partition_3 "
        "    LEFT JOIN "
        f"   {os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_CLICKS")} nbc "
        "    ON "
        "    nbi.auction_id = nbc.auction_id "
        "    AND nbi.partition_0 = nbc.partition_0 "
        "    AND nbi.partition_1 = nbc.partition_1 "
        "    AND nbi.partition_2 = nbc.partition_2 "
        "    AND nbi.partition_3 = nbc.partition_3 "
        "    WHERE "
        f"     nbi.partition_0 = '{event['partition_0']}' "
        f"     AND nbi.partition_1 = '{event['partition_1']}' "
        f"     AND nbi.partition_2 = '{event['partition_2']}' "
        f"     AND nbi.partition_3 = '{event['partition_3']}' "
        "  ) "
        "  LEFT JOIN \"datalake_raw\".\"google_ad_units\" ga "
        "  ON ga.id = cast(ad_unit_id as varchar)"
        "  ORDER BY "
        "  year, month, day, hour, auction_id "
    )
    event['query'] = {}
    event['query']["QueryString"] = sql

    return event
