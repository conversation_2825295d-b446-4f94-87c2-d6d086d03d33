import os

def lambda_handler(event, context):
    raw_data_table_name = event['table_name']

    impressions_columns =  [
        ("auction_id", "string"),
        ("measurable_view", "int"),
        ("display_or_video", "string"),
        ("eligible_view", "int"),
        ("revenue", "decimal(38,20)"),
        ("impression", "int"),
        ("line_item_id", "string"),
        ("advertiser_id", "bigint"),
        ("hb_bidder", "string"),
        ("ad_unit_id", "bigint"),
        ("deal_id", "bigint"),
        ("year", "string"),
        ("month", "string"),
        ("day", "string"),
        ("hour", "string"),
    ]

    clicks_columns = [
        ("auction_id", "string"),
        ("click", "int"),
        ("line_item_id", "string"),
        ("year", "string"),
        ("month", "string"),
        ("day", "string"),
        ("hour", "string"),
    ]

    active_views_columns = [
        ("auction_id", "string"),
        ("measurable_view", "int"),
        ("display_or_video", "string"),
        ("viewable_view", "int"),
        ("year", "string"),
        ("month", "string"),
        ("day", "string"),
        ("hour", "string"),
    ]

    multi_key_value_columns = [
        ("auction_id", "string"),
        ("line_item_id", "string"),
        ("publisher", "string"),
        ("measurable_view", "int"),
        ("viewable_view", "int"),
        ("eligible_view", "int"),
        ("click", "int"),
        ("impression", "int"),
        ("revenue", "decimal(38,20)"),
        ("year", "string"),
        ("month", "string"),
        ("day", "string"),
        ("hour", "string"),
    ]

    raw_data_temp_table_pairs = {
        os.environ.get("RAW_DATA_NETWORK_IMPRESSIONS"): os.environ.get("TEMP_TABLE_NETWORK_IMPRESSIONS"),
        os.environ.get("RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS"): os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS"),
        os.environ.get("RAW_DATA_NETWORK_CLICKS"): os.environ.get("TEMP_TABLE_NETWORK_CLICKS"),
        os.environ.get("RAW_DATA_NETWORK_BACKFILL_CLICKS"): os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_CLICKS"),
        os.environ.get("RAW_DATA_NETWORK_ACTIVE_VIEWS"): os.environ.get("TEMP_TABLE_NETWORK_ACTIVE_VIEWS"),
        os.environ.get("RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS"): os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS"),
        os.environ.get("TABLE_MULTI_KEY_VALUE_GOOGLE"): os.environ.get("TABLE_MULTI_KEY_VALUE_GOOGLE"),
    }

    temp_table_columns = {
        os.environ.get("TEMP_TABLE_NETWORK_IMPRESSIONS"): impressions_columns,
        os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS"): impressions_columns,
        os.environ.get("TEMP_TABLE_NETWORK_CLICKS"): clicks_columns,
        os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_CLICKS"): clicks_columns,
        os.environ.get("TEMP_TABLE_NETWORK_ACTIVE_VIEWS"): active_views_columns,
        os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS"): active_views_columns,
        os.environ.get("TABLE_MULTI_KEY_VALUE_GOOGLE"): multi_key_value_columns,
    }

    temp_table_name = raw_data_temp_table_pairs[raw_data_table_name]

    columns_definition = ", ".join([f"`{col[0]}` {col[1]}" for col in temp_table_columns[temp_table_name]])
    query_string = (
        f"CREATE EXTERNAL TABLE IF NOT EXISTS `{temp_table_name}` ("
        f"{columns_definition}"
        ") PARTITIONED BY ("
        "`partition_0` string, "
        "`partition_1` string, "
        "`partition_2` string, "
        "`partition_3` string"
        ") ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' "
        "STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' "
        "OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat' "
        f"LOCATION 's3://{os.environ.get("PROCESSING_BUCKET_NAME")}/{temp_table_name}' "
        "TBLPROPERTIES ('parquet.compression'='ZSTD', 'compression_level' = '22', 'partition_filtering.enabled' = 'true')"
    )

    event['query']['QueryString'] = query_string

    return event
