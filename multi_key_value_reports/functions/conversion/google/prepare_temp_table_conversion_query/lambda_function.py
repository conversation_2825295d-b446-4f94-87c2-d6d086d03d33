import os

def lambda_handler(event, context):
    table_name = event['table_name']
    file_name_year = event['partition_0']
    file_name_month = event['partition_1']
    file_name_day = event['partition_2']
    file_name_hour = event['partition_3']

    if table_name == os.environ.get("RAW_DATA_NETWORK_IMPRESSIONS"):
        event['query']['QueryString'] = (
            f"INSERT INTO {table_name}_temp"
            "( "
            "  auction_id, "
            "  measurable_view, "
            "  display_or_video, "
            "  eligible_view, "
            "  revenue, "
            "  impression, "
            "  line_item_id, "
            "  advertiser_id, "
            "  hb_bidder, "
            "  ad_unit_id, "
            "  deal_id,"
            "  year, "
            "  month, "
            "  day, "
            "  hour, "
            "  partition_0, "
            "  partition_1, "
            "  partition_2, "
            "  partition_3 "
            ") "
            "  SELECT"
            "    regexp_extract(CustomTargeting, '(auction_id=)([0-9]*)(;)', 2) AS auction_id,"
            "    ActiveViewMeasurableCount AS measurable_view,"
            "    CASE"
            "      WHEN CreativeSizeDelivered LIKE 'Video/Overlay' THEN 'video'"
            "      ELSE 'display'"
            "    END AS display_or_video,"
            "    ActiveViewEligibleCount AS eligible_view,"
            "    CAST(EstimatedBackfillRevenue AS decimal(38,20)) AS revenue,"
            "    1 AS impression,"
            "    CAST(LineItemId AS VARCHAR) AS line_item_id,"
            "    AdvertiserId AS advertiser_id,"
            "    regexp_extract(CustomTargeting, '(hb_bidder=)([a-zA-Z]*)(;)',2) AS hb_bidder,"
            "    AdUnitId AS ad_unit_id,"
            "    DealId AS deal_id,"
            "    SUBSTRING(\"Time\", 1, 4) AS year,"
            "    SUBSTRING(\"Time\", 6, 2) AS month,"
            "    SUBSTRING(\"Time\", 9, 2) AS day,"
            "    SUBSTRING(\"Time\", 12, 2) AS hour,"
            f"   '{file_name_year}' AS partition_0,"
            f"   '{file_name_month}' AS partition_1,"
            f"   '{file_name_day}' AS partition_2,"
            f"   '{file_name_hour}' AS partition_3"
            "  FROM ("
            "    SELECT"
            "      \"Time\","
            "      EstimatedBackfillRevenue,"
            "      CustomTargeting,"
            "      CreativeSizeDelivered,"
            "      ActiveViewMeasurableCount,"
            "      ActiveViewEligibleCount,"
            "      LineItemId,"
            "      AdvertiserId,"
            "      AdUnitId,"
            "      DealId"
            "    FROM network_impressions"
            "    WHERE  LineItemId != 0"
            f"     AND partition_0 LIKE '{file_name_year}'"
            f"     AND partition_1 LIKE '{file_name_month}'"
            f"     AND partition_2 LIKE '{file_name_day}'"
            f"     AND partition_3 LIKE '{file_name_hour}'"
            "  ) AS subquery"
        )
    elif table_name == os.environ.get("RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS"):
        event['query']['QueryString'] = (
            f"INSERT INTO {table_name}_temp"
            "( "
            "  auction_id, "
            "  measurable_view, "
            "  display_or_video, "
            "  eligible_view, "
            "  revenue, "
            "  impression, "
            "  line_item_id, "
            "  advertiser_id, "
            "  hb_bidder, "
            "  ad_unit_id, "
            "  deal_id, "
            "  year, "
            "  month, "
            "  day, "
            "  hour, "
            "  partition_0, "
            "  partition_1, "
            "  partition_2, "
            "  partition_3 "
            ") "
            "  SELECT"
            "    regexp_extract(CustomTargeting, '(auction_id=)([0-9]*)(;)', 2) AS auction_id,"
            "    ActiveViewMeasurableCount AS measurable_view,"
            "    CASE"
            "      WHEN CreativeSizeDelivered LIKE 'Video/Overlay' THEN 'video'"
            "      ELSE 'display'"
            "    END AS display_or_video,"
            "    ActiveViewEligibleCount AS eligible_view,"
            "    CAST(EstimatedBackfillRevenue AS decimal(38,20)) AS revenue,"
            "    1 AS impression,"
            "    CAST(LineItemId AS VARCHAR) AS line_item_id,"
            "    AdvertiserId AS advertiser_id,"
            "    regexp_extract(CustomTargeting, '(hb_bidder=)([a-zA-Z]*)(;)',2) AS hb_bidder,"
            "    AdUnitId AS ad_unit_id,"
            "    DealId AS deal_id,"
            "    SUBSTRING(\"Time\", 1, 4) AS year,"
            "    SUBSTRING(\"Time\", 6, 2) AS month,"
            "    SUBSTRING(\"Time\", 9, 2) AS day,"
            "    SUBSTRING(\"Time\", 12, 2) AS hour,"
            f"   '{file_name_year}' AS partition_0,"
            f"   '{file_name_month}' AS partition_1,"
            f"   '{file_name_day}' AS partition_2,"
            f"   '{file_name_hour}' AS partition_3"
            "  FROM ("
            "    SELECT"
            "      \"Time\","
            "      EstimatedBackfillRevenue,"
            "      CustomTargeting,"
            "      CreativeSizeDelivered,"
            "      ActiveViewMeasurableCount,"
            "      ActiveViewEligibleCount,"
            "      LineItemId,"
            "      AdvertiserId,"
            "      AdUnitId,"
            "      DealId"
            "    FROM network_backfill_impressions"
            f"   WHERE partition_0 LIKE '{file_name_year}'"
            f"     AND partition_1 LIKE '{file_name_month}'"
            f"     AND partition_2 LIKE '{file_name_day}'"
            f"     AND partition_3 LIKE '{file_name_hour}'"
            "  ) AS subquery"
        )
    elif table_name == os.environ.get("RAW_DATA_NETWORK_CLICKS") or table_name == os.environ.get("RAW_DATA_NETWORK_BACKFILL_CLICKS"):
        event['query']['QueryString'] = (
            f"INSERT INTO {table_name}_temp"
            "  SELECT"
            "    regexp_extract(CustomTargeting, '(auction_id=)([0-9]*)(;)', 2) AS auction_id,"
            "    1 AS click,"
            "    CAST(LineItemId AS VARCHAR) AS line_item_id,"
            "    SUBSTRING(\"Time\", 1, 4) AS year,"
            "    SUBSTRING(\"Time\", 6, 2) AS month,"
            "    SUBSTRING(\"Time\", 9, 2) AS day,"
            "    SUBSTRING(\"Time\", 12, 2) AS hour,"
            f"   '{file_name_year}' AS partition_0,"
            f"   '{file_name_month}' AS partition_1,"
            f"   '{file_name_day}' AS partition_2,"
            f"   '{file_name_hour}' AS partition_3"
            "  FROM ("
            "    SELECT"
            "      \"Time\","
            "      CustomTargeting,"
            "      LineItemId"
            f"   FROM {table_name}"
            f"   WHERE partition_0 LIKE '{file_name_year}'"
            f"     AND partition_1 LIKE '{file_name_month}'"
            f"     AND partition_2 LIKE '{file_name_day}'"
            f"     AND partition_3 LIKE '{file_name_hour}'"
            "  ) AS subquery"
        )
    elif table_name == os.environ.get("RAW_DATA_NETWORK_ACTIVE_VIEWS") or table_name == os.environ.get("RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS"):
        event['query']['QueryString'] = (
            f"INSERT INTO {table_name}_temp"
            "  SELECT"
            "    regexp_extract(CustomTargeting, '(auction_id=)([0-9]*)(;)', 2) AS auction_id,"
            "    ActiveViewMeasurableCount AS measurable_view,"
            "    CASE"
            "      WHEN CreativeSizeDelivered LIKE 'Video/Overlay' THEN 'video'"
            "      ELSE 'display'"
            "    END AS display_or_video,"
            "    ActiveViewViewableCount AS viewable_view,"
            "    SUBSTRING(\"Time\", 1, 4) AS year,"
            "    SUBSTRING(\"Time\", 6, 2) AS month,"
            "    SUBSTRING(\"Time\", 9, 2) AS day,"
            "    SUBSTRING(\"Time\", 12, 2) AS hour,"
            f"   '{file_name_year}' AS partition_0,"
            f"   '{file_name_month}' AS partition_1,"
            f"   '{file_name_day}' AS partition_2,"
            f"   '{file_name_hour}' AS partition_3"
            "  FROM ("
            "    SELECT"
            "      \"Time\","
            "      CustomTargeting,"
            "      CreativeSizeDelivered,"
            "      ActiveViewMeasurableCount,"
            "      ActiveViewViewableCount"
            f"    FROM {table_name}"
            f"   WHERE partition_0 LIKE '{file_name_year}'"
            f"     AND partition_1 LIKE '{file_name_month}'"
            f"     AND partition_2 LIKE '{file_name_day}'"
            f"     AND partition_3 LIKE '{file_name_hour}'"
            "  ) AS subquery"
        )
    else:
        raise ValueError(f"Unknown table name: {table_name}")

    return event
