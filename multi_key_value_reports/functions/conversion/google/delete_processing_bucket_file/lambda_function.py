import boto3
import os

s3_client = boto3.client('s3')

def lambda_handler(event, context):
    bucket_name = os.environ.get('BUCKET_NAME')
    path = event['path']

    response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=path)
    if 'Contents' in response:
        for item in response['Contents']:
            s3_client.delete_object(Bucket=bucket_name, Key=item['Key'])

    return event
