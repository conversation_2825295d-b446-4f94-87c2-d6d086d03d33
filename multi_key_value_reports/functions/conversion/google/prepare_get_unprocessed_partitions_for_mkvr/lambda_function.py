import os


def lambda_handler(event, context):
    event['query']['QueryString'] = (
        "WITH all_network_partitions AS ("
        "  SELECT partition_0, partition_1, partition_2, partition_3 "
        f" FROM \"{os.environ.get("TEMP_TABLE_NETWORK_IMPRESSIONS")}$partitions\" "
        "  WHERE date(concat_ws('-', partition_0, partition_1, partition_2)) > (CURRENT_DATE - INTERVAL '7' DAY)"
        "  INTERSECT "
        "  SELECT partition_0, partition_1, partition_2, partition_3 "
        f" FROM \"{os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS")}$partitions\" "
        "  WHERE date(concat_ws('-', partition_0, partition_1, partition_2)) > (CURRENT_DATE - INTERVAL '7' DAY)"
        "  INTERSECT "
        "  SELECT partition_0, partition_1, partition_2, partition_3 "
        f" FROM \"{os.environ.get("TEMP_TABLE_NETWORK_CLICKS")}$partitions\" "
        "  WHERE date(concat_ws('-', partition_0, partition_1, partition_2)) > (CURRENT_DATE - INTERVAL '7' DAY)"
        "  INTERSECT "
        "  SELECT partition_0, partition_1, partition_2, partition_3 "
        f" FROM \"{os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_CLICKS")}$partitions\" "
        "  WHERE date(concat_ws('-', partition_0, partition_1, partition_2)) > (CURRENT_DATE - INTERVAL '7' DAY)"
        "  INTERSECT "
        "  SELECT partition_0, partition_1, partition_2, partition_3 "
        f" FROM \"{os.environ.get("TEMP_TABLE_NETWORK_ACTIVE_VIEWS")}$partitions\" "
        "  WHERE date(concat_ws('-', partition_0, partition_1, partition_2)) > (CURRENT_DATE - INTERVAL '7' DAY)"
        "  INTERSECT "
        "  SELECT partition_0, partition_1, partition_2, partition_3 "
        f" FROM \"{os.environ.get("TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS")}$partitions\" "
        "  WHERE date(concat_ws('-', partition_0, partition_1, partition_2)) > (CURRENT_DATE - INTERVAL '7' DAY)"
        ") "
        "SELECT partition_0, partition_1, partition_2, partition_3 "
        "FROM all_network_partitions "
        "EXCEPT "
        "SELECT partition_0, partition_1, partition_2, partition_3 "
        f"FROM \"{os.environ.get("TABLE_MULTI_KEY_VALUE_GOOGLE")}$partitions\" "
        "ORDER BY partition_0 DESC, partition_1 DESC, partition_2 DESC, partition_3 DESC "
    )
    return event
