import re
import boto3
import os
import json

step_functions_client = boto3.client('stepfunctions')


def to_snake_case(name):
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def lambda_handler(event, context):

    # Extract the file name from the S3 event
    bucket_name = event['Records'][0]['s3']['bucket']['name']
    object_key = event['Records'][0]['s3']['object']['key'] # 'tmp/gcs/raw/data/network_clicks/2024/10/01/00/NetworkClicks_335074_20241001_00.gz'
    object_key_split = object_key.split("/")
    file_name = object_key_split[9]

    pattern = r"(NetworkImpressions|NetworkBackfillImpressions|NetworkClicks|NetworkBackfillClicks|NetworkActiveViews|NetworkBackfillActiveViews)_\d+_(\d{8})_(\d{2})(_corrected)?\.gz"
    match = re.match(pattern, file_name)

    table_name = to_snake_case(match.group(1))
    date_str = match.group(2)
    partition_0 = date_str[:4]  # year
    partition_1 = date_str[4:6]  # month
    partition_2 = date_str[6:]  # day
    partition_3 = match.group(3)  # hour
    is_corrected = match.group(4) is not None

    event = {
        "bucket_name": bucket_name,
        "table_name": table_name,
        "path": "/".join(object_key_split[:9]), # 'tmp/gcs/raw/data/network_clicks/2024/10/01/00
        "partition_0": partition_0,
        "partition_1": partition_1,
        "partition_2": partition_2,
        "partition_3": partition_3,
        "is_corrected": is_corrected
    }

    step_functions_client.start_execution(
        stateMachineArn=os.environ['CONVERSION_STATE_MACHINE_ARN'],
        input=json.dumps(event)
    )
