import pytz
from datetime import datetime


def lambda_handler(event, context):

    timezone = pytz.timezone('Europe/Berlin')
    now = datetime.now(timezone)
    is_dst = bool(now.dst())
    hour_to_utc = -2 if is_dst else -1

    # 1. Get partitions from Microsoft standard_feed_parquet_temp that are not in as_dimensions
    # 2. For each Google event date (= when auctions happened = partition in Microsoft standard_feed_parquet_temp),
    # calculate the rows in each Google partition where the event date is present
    # 3. Join the two tables on the event date
    # 4. Sum up the rows for each Microsoft partition
    # 5. Filter rows where the length of Google partitions is larger than or equal to 2,
    # because Google delivers around 99% of auction events in the first 2 partitions,
    # the rest 1% of events are delivered in later partitions, which we will ignore it / we will not backfill it
    query_string = (
        "WITH unprocessed_partitions AS ( "
        "   SELECT concat(partition_0, '-', partition_1, '-', partition_2, ' ', partition_3) AS m_partition "
        "   FROM ( "
        "       SELECT partition_0, partition_1, partition_2, partition_3 "
        "       FROM \"standard_feed_parquet_temp$partitions\" "
        "       WHERE date_parse(concat(partition_0, '-', partition_1, '-', partition_2, ' ', partition_3), '%Y-%m-%d %H') >= date_add('day', -10, current_timestamp)"
        "   ) "
        "EXCEPT "
        "   SELECT concat(date, ' ', hour) AS as_partition "
        "   FROM ( "
        "       SELECT date, hour "
        "       FROM \"as_dimensions$partitions\" "
        "   ) "
        "), "
        "google_impressions AS ( "
        "   SELECT "
        "       count(*) AS row_count, "
        "       concat(year, '-', month, '-', day, ' ', hour) as event_time, "
        f"       date_format(date_add('hour', {hour_to_utc}, date_parse(concat(year, '-', month, '-', day, ' ', hour), '%Y-%m-%d %H')), '%Y-%m-%d %H') as event_time_utc, "
        "       concat(partition_0, '-', partition_1, '-', partition_2, ' ', partition_3) as g_partition, "
        "       source "
        "   FROM ( "
        "       SELECT year, month, day, hour, partition_0, partition_1, partition_2, partition_3, 'network_impressions_temp' as source "
        "       FROM network_impressions_temp "
        "       UNION ALL "
        "       SELECT year, month, day, hour, partition_0, partition_1, partition_2, partition_3, 'network_backfill_impressions_temp' as source "
        "       FROM network_backfill_impressions_temp "
        "   ) subquery "
        "   GROUP BY year, month, day, hour, partition_0, partition_1, partition_2, partition_3, source "
        "), "
        "joined_table AS ( "
        "   SELECT * "
        "   FROM google_impressions "
        "   LEFT JOIN unprocessed_partitions ON event_time_utc = m_partition "
        "   WHERE m_partition is not null AND row_count > 100 "
        "), "
        "sumup_table AS ( "
        "   SELECT "
        "   sum(row_count) AS row_count, "
        "   ARRAY_AGG(distinct g_partition) AS g_partitions, "
        "   ARRAY_AGG(distinct source) AS sources, "
        "   m_partition, "
        "   event_time "
        "   FROM joined_table "
        "   GROUP BY m_partition, event_time "
        ") "
        "SELECT * "
        "FROM sumup_table "
        "WHERE cardinality(g_partitions) >= 2 AND cardinality(sources) = 2 "
        "ORDER BY m_partition desc "
    )

    event['query']['QueryString'] = query_string

    return event
