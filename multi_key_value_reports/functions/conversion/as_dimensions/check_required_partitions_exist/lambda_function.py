import json
import logging
import boto3
from botocore.exceptions import ClientError

logger = logging.getLogger()
logger.setLevel(logging.INFO)

s3_client = boto3.client('s3')

def lambda_handler(event, context):
    """
    Lambda function to check if required partitions exist before AS Dimensions update.
    
    This function implements step 6 from code_assistant.md:
    - Verifies that m_partition_to_update exists in standard_feed_parquet_temp
    - Verifies that all partitions in original_g_partitions_used exist in their respective Google temp tables
    - Fails if any required partitions are missing
    
    Args:
        event (dict): Event containing:
            - m_partition_date: Date part of Microsoft partition (YYYY-MM-DD)
            - m_partition_hour: Hour part of Microsoft partition (HH)
            - original_g_partitions_list: List of Google partition strings
            - bucket_name: S3 bucket name
            
    Returns:
        dict: Event with validation results
    """
    try:
        logger.info(f"Checking required partitions exist: {json.dumps(event)}")
        
        # Extract required information from event
        m_partition_date = event.get('m_partition_date')
        m_partition_hour = event.get('m_partition_hour')
        original_g_partitions_list = event.get('original_g_partitions_list', [])
        bucket_name = event.get('bucket_name')
        
        if not all([m_partition_date, m_partition_hour, bucket_name]):
            raise ValueError("Missing required fields: m_partition_date, m_partition_hour, bucket_name")
        
        missing_partitions = []
        
        # Check if Microsoft partition exists in standard_feed_parquet_temp
        logger.info(f"Checking Microsoft partition: {m_partition_date} {m_partition_hour}")
        
        # Parse date components for Microsoft partition path
        year, month, day = m_partition_date.split('-')
        ms_partition_prefix = f"standard_feed_parquet_temp/partition_0={year}/partition_1={month}/partition_2={day}/partition_3={m_partition_hour}/"
        
        if not _check_s3_prefix_exists(bucket_name, ms_partition_prefix):
            missing_partitions.append(f"Microsoft partition: {m_partition_date} {m_partition_hour}")
            logger.error(f"Microsoft partition not found: {ms_partition_prefix}")
        else:
            logger.info(f"Microsoft partition found: {ms_partition_prefix}")
        
        # Check if all Google partitions exist in their respective temp tables
        google_temp_tables = [
            'network_impressions_temp',
            'network_clicks_temp', 
            'network_active_views_temp',
            'network_backfill_impressions_temp',
            'network_backfill_clicks_temp',
            'network_backfill_active_views_temp'
        ]
        
        for g_partition in original_g_partitions_list:
            logger.info(f"Checking Google partition: {g_partition}")
            
            # Parse Google partition string (format: "YYYY-MM-DD HH")
            try:
                g_date_part, g_hour_part = g_partition.split(' ')
                g_year, g_month, g_day = g_date_part.split('-')
            except ValueError:
                logger.error(f"Invalid Google partition format: {g_partition}")
                missing_partitions.append(f"Invalid Google partition format: {g_partition}")
                continue
            
            # Check if this Google partition exists in at least one of the temp tables
            partition_found = False
            for table in google_temp_tables:
                g_partition_prefix = f"{table}/partition_0={g_year}/partition_1={g_month}/partition_2={g_day}/partition_3={g_hour_part}/"
                
                if _check_s3_prefix_exists(bucket_name, g_partition_prefix):
                    logger.info(f"Google partition found in {table}: {g_partition_prefix}")
                    partition_found = True
                    break
            
            if not partition_found:
                missing_partitions.append(f"Google partition: {g_partition}")
                logger.error(f"Google partition not found in any temp table: {g_partition}")
        
        # Check results
        if missing_partitions:
            error_message = f"Missing required partitions: {missing_partitions}"
            logger.error(error_message)
            raise ValueError(error_message)
        
        logger.info("All required partitions exist. Proceeding with AS Dimensions update.")
        event['partitions_validated'] = True
        
        return event
        
    except Exception as e:
        logger.error(f"Error checking required partitions: {str(e)}")
        raise


def _check_s3_prefix_exists(bucket_name, prefix):
    """
    Check if any objects exist with the given prefix in S3.
    
    Args:
        bucket_name (str): S3 bucket name
        prefix (str): S3 prefix to check
        
    Returns:
        bool: True if objects exist with the prefix, False otherwise
    """
    try:
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=prefix,
            MaxKeys=1
        )
        return response.get('KeyCount', 0) > 0
    except ClientError as e:
        logger.error(f"Error checking S3 prefix {prefix}: {str(e)}")
        return False
