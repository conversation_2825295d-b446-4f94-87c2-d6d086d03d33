import json
import logging
import boto3
from botocore.exceptions import Client<PERSON>rror

from insighter_commons.constants import (
    TEMP_TABLE_NETWORK_IMPRESSIONS,
    TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS
)

logger = logging.getLogger()
logger.setLevel(logging.INFO)

s3_client = boto3.client('s3')

def lambda_handler(event, context):
    """
    Lambda function to check if required partitions exist before AS Dimensions update.

    This function implements steps 5 & 6 from code_assistant.md:
    - Reads the CSV row data from Athena results containing m_partition_to_update and original_g_partitions_used
    - Verifies that m_partition_to_update exists in standard_feed_parquet_temp
    - Verifies that all partitions in original_g_partitions_used exist in BOTH required Google temp tables:
      * network_impressions_temp
      * network_backfill_impressions_temp
    - Fails if any required partitions are missing from any table

    Args:
        event (dict): Event containing CSV row data from Athena results:
            - m_partition_to_update: Microsoft partition string (YYYY-MM-DD HH)
            - original_g_partitions_used: Array string of Google partition strings
            - bucket_name: S3 bucket name (from Step Function input)

    Returns:
        dict: Event with validation results and parsed partition information
    """
    try:
        logger.info(f"Checking required partitions exist: {json.dumps(event)}")

        # Extract required information from event (CSV row data)
        m_partition_to_update = event.get('m_partition_to_update')
        original_g_partitions_used = event.get('original_g_partitions_used')
        bucket_name = event.get('bucket_name')

        if not all([m_partition_to_update, original_g_partitions_used, bucket_name]):
            raise ValueError("Missing required fields: m_partition_to_update, original_g_partitions_used, bucket_name")

        # Parse the m_partition_to_update to extract date and hour
        # Format is expected to be "YYYY-MM-DD HH"
        try:
            m_partition_date, m_partition_hour = m_partition_to_update.split(' ')
        except ValueError:
            logger.error(f"Invalid m_partition_to_update format: {m_partition_to_update}")
            raise ValueError(f"m_partition_to_update must be in format 'YYYY-MM-DD HH', got: {m_partition_to_update}")

        # Parse the original_g_partitions_used array
        # This should be a string representation of an array like "[2023-10-27 15, 2023-10-27 16]"
        try:
            # Remove brackets and split by comma
            if original_g_partitions_used.startswith('[') and original_g_partitions_used.endswith(']'):
                partitions_str = original_g_partitions_used[1:-1]  # Remove brackets
                if partitions_str.strip():  # Check if not empty
                    # Split by comma and clean up whitespace
                    original_g_partitions_list = [p.strip() for p in partitions_str.split(',')]
                else:
                    original_g_partitions_list = []
            else:
                # If not in array format, treat as single partition
                original_g_partitions_list = [original_g_partitions_used.strip()]
        except Exception as e:
            logger.error(f"Error parsing original_g_partitions_used: {original_g_partitions_used}, error: {str(e)}")
            raise ValueError(f"Error parsing original_g_partitions_used: {str(e)}")

        logger.info(f"Microsoft partition to update: {m_partition_to_update}")
        logger.info(f"Parsed Google partitions list: {original_g_partitions_list}")

        missing_partitions = []

        # Check if Microsoft partition exists in standard_feed_parquet_temp
        logger.info(f"Checking Microsoft partition: {m_partition_date} {m_partition_hour}")

        # Parse date components for Microsoft partition path
        year, month, day = m_partition_date.split('-')
        ms_partition_prefix = f"standard_feed_parquet_temp/partition_0={year}/partition_1={month}/partition_2={day}/partition_3={m_partition_hour}/"

        if not _check_s3_prefix_exists(bucket_name, ms_partition_prefix):
            missing_partitions.append(f"Microsoft partition: {m_partition_date} {m_partition_hour}")
            logger.error(f"Microsoft partition not found: {ms_partition_prefix}")
        else:
            logger.info(f"Microsoft partition found: {ms_partition_prefix}")

        # Check if all Google partitions exist in both required temp tables
        required_google_temp_tables = [
            TEMP_TABLE_NETWORK_IMPRESSIONS,
            TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS
        ]

        for g_partition in original_g_partitions_list:
            logger.info(f"Checking Google partition: {g_partition}")

            # Parse Google partition string (format: "YYYY-MM-DD HH")
            try:
                g_date_part, g_hour_part = g_partition.split(' ')
                g_year, g_month, g_day = g_date_part.split('-')
            except ValueError:
                logger.error(f"Invalid Google partition format: {g_partition}")
                missing_partitions.append(f"Invalid Google partition format: {g_partition}")
                continue

            # Check if this Google partition exists in ALL required temp tables
            for table in required_google_temp_tables:
                g_partition_prefix = f"{table}/partition_0={g_year}/partition_1={g_month}/partition_2={g_day}/partition_3={g_hour_part}/"

                if not _check_s3_prefix_exists(bucket_name, g_partition_prefix):
                    missing_partitions.append(f"Google partition: {g_partition} in table: {table}")
                    logger.error(f"Google partition not found in {table}: {g_partition_prefix}")
                else:
                    logger.info(f"Google partition found in {table}: {g_partition_prefix}")

        # Check results
        if missing_partitions:
            error_message = f"Missing required partitions: {missing_partitions}"
            logger.error(error_message)
            raise ValueError(error_message)

        logger.info("All required partitions exist. Proceeding with AS Dimensions update.")

        # Add parsed partition information to the event for downstream processing
        event['partitions_validated'] = True
        event['m_partition_date'] = m_partition_date
        event['m_partition_hour'] = m_partition_hour
        event['original_g_partitions_list'] = original_g_partitions_list

        return event

    except Exception as e:
        logger.error(f"Error checking required partitions: {str(e)}")
        raise


def _check_s3_prefix_exists(bucket_name, prefix):
    """
    Check if any objects exist with the given prefix in S3.

    Args:
        bucket_name (str): S3 bucket name
        prefix (str): S3 prefix to check

    Returns:
        bool: True if objects exist with the prefix, False otherwise
    """
    try:
        response = s3_client.list_objects_v2(
            Bucket=bucket_name,
            Prefix=prefix,
            MaxKeys=1
        )
        return response.get('KeyCount', 0) > 0
    except ClientError as e:
        logger.error(f"Error checking S3 prefix {prefix}: {str(e)}")
        return False
