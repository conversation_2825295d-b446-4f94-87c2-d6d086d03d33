import os

def lambda_handler(event, context):
    # date and hour are used to join mkvr_microsoft table
    # g_partitions are used to update data once google sends corrected data
    query_string = (
        "CREATE EXTERNAL TABLE IF NOT EXISTS `as_dimensions` ("
        "  `m_auction_id` bigint, "
        "  `as_impression_type` string, "
        "  `as_supply_type` string, "
        "  `as_supply_path` string, "
        "  `as_market_type` string, "
        "  `as_ssp` string, "
        "  `as_page_type` string, "
        "  `g_partition_0` string, "
        "  `g_partition_1` string, "
        "  `g_partition_2` string, "
        "  `g_partition_3` string "
        ") PARTITIONED BY ("
        "  `date` string, "
        "  `hour` string "
        ") ROW FORMAT SERDE 'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe' "
        "STORED AS INPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat' "
        "OUTPUTFORMAT 'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat' "
        f"LOCATION 's3://{os.environ.get("PROCESSING_BUCKET_NAME")}/as_dimensions' "
        "TBLPROPERTIES ('parquet.compression'='ZSTD', 'compression_level' = '22', 'partition_filtering.enabled' = 'true')"
    )

    event['query'] = {}
    event['query']['QueryString'] = query_string

    return event
