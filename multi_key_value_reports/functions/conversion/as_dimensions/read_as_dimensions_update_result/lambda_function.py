import json
import logging

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
    """
    Lambda function to read AS Dimensions update query results.
    
    This function implements step 5 from code_assistant.md:
    - Reads the result of the Athena query that finds affected AS Dimensions partitions
    - If the result is empty, indicates no update is needed
    - If a result row is found, extracts m_partition_to_update and original_g_partitions_used
    - The result file is expected to have at least one row and maximum two rows
    
    Args:
        event (dict): Event containing the CSV row data from Athena results
        context (LambdaContext): The Lambda context
        
    Returns:
        dict: Event with extracted partition information or indication of no update needed
    """
    try:
        logger.info(f"Processing AS Dimensions update result: {json.dumps(event)}")
        
        # The event should contain the CSV row data from the Athena results
        # Expected format: m_partition_to_update, original_g_partitions_used
        
        # Check if we have the required fields from the CSV row
        if 'm_partition_to_update' not in event or 'original_g_partitions_used' not in event:
            logger.error(f"Missing required fields in event: {event}")
            raise ValueError("Event must contain 'm_partition_to_update' and 'original_g_partitions_used' fields")
        
        m_partition_to_update = event['m_partition_to_update']
        original_g_partitions_used = event['original_g_partitions_used']
        
        logger.info(f"Found AS Dimensions partition to update: {m_partition_to_update}")
        logger.info(f"Original Google partitions used: {original_g_partitions_used}")
        
        # Parse the m_partition_to_update to extract date and hour
        # Format is expected to be "YYYY-MM-DD HH"
        try:
            date_part, hour_part = m_partition_to_update.split(' ')
            event['m_partition_date'] = date_part
            event['m_partition_hour'] = hour_part
        except ValueError:
            logger.error(f"Invalid m_partition_to_update format: {m_partition_to_update}")
            raise ValueError(f"m_partition_to_update must be in format 'YYYY-MM-DD HH', got: {m_partition_to_update}")
        
        # Parse the original_g_partitions_used array
        # This should be a string representation of an array like "[2023-10-27 15, 2023-10-27 16]"
        try:
            # Remove brackets and split by comma
            if original_g_partitions_used.startswith('[') and original_g_partitions_used.endswith(']'):
                partitions_str = original_g_partitions_used[1:-1]  # Remove brackets
                if partitions_str.strip():  # Check if not empty
                    # Split by comma and clean up whitespace
                    partitions_list = [p.strip() for p in partitions_str.split(',')]
                    event['original_g_partitions_list'] = partitions_list
                else:
                    event['original_g_partitions_list'] = []
            else:
                # If not in array format, treat as single partition
                event['original_g_partitions_list'] = [original_g_partitions_used.strip()]
        except Exception as e:
            logger.error(f"Error parsing original_g_partitions_used: {original_g_partitions_used}, error: {str(e)}")
            raise ValueError(f"Error parsing original_g_partitions_used: {str(e)}")
        
        logger.info(f"Parsed Google partitions list: {event['original_g_partitions_list']}")
        
        # Add flag to indicate that update is needed
        event['update_needed'] = True
        
        return event
        
    except Exception as e:
        logger.error(f"Error processing AS Dimensions update result: {str(e)}")
        raise
