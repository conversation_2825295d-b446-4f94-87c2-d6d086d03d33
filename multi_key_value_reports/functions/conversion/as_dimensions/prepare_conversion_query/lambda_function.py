def lambda_handler(event, context):

    google_template = (
        "   SELECT"
        "       auction_id as g_auction_id,"
        "       advertiser_id as g_advertiser_id,"
        "       hb_bidder as g_hb_bidder,"
        "       deal_id as g_deal_id,"
        "       partition_0 as g_partition_0,"
        "       partition_1 as g_partition_1,"
        "       partition_2 as g_partition_2,"
        "       partition_3 as g_partition_3"
        "   FROM {temp_table_name}"
        "   WHERE partition_0 = '{g_partition_0}' AND partition_1 = '{g_partition_1}' AND partition_2 = '{g_partition_2}' AND partition_3 = '{g_partition_3}'"
        "   AND year = '{year}' AND month = '{month}' AND day = '{day}' AND hour = '{hour}' "
        "   AND auction_id IS NOT NULL"
    )

    formated_templates = []

    google_tables = event['sources'].strip('[]').split(', ')
    g_partitions = event['g_partitions'].strip('[]').split(', ')
    g_event_time = event['event_time'].split(' ')
    m_partition = event['m_partition'].split(' ')

    for partition_str in g_partitions:
        for table_name in google_tables:
            formated_template = google_template.format(
                temp_table_name = table_name,
                g_partition_0 = partition_str[0:4],
                g_partition_1 = partition_str[5:7],
                g_partition_2 = partition_str[8:10],
                g_partition_3 = partition_str[11:13],
                year = g_event_time[0][0:4],
                month = g_event_time[0][5:7],
                day = g_event_time[0][8:10],
                hour = g_event_time[1]
            )
            formated_templates.append(formated_template)

    union_google_select_statements = " UNION ALL ".join(formated_templates)

    query_string = (
        "INSERT INTO as_dimensions "
        "( "
        "   m_auction_id, "
        "   as_impression_type, "
        "   as_supply_type, "
        "   as_supply_path, "
        "   as_market_type, "
        "   as_ssp, "
        "   as_page_type, "
        "   g_partition_0, "
        "   g_partition_1, "
        "   g_partition_2, "
        "   g_partition_3, "
        "   date, "
        "   hour "
        ")"
        "SELECT "
        "   m_auction_id, "
        "   as_impression_type, "
        "   as_supply_type, "
        "   as_supply_path, "
        "   CASE "
        "       WHEN as_impression_type = 'direct' THEN 'direct business' "
        "       WHEN as_supply_path LIKE 'gam_%' THEN g_market_type "
        "       WHEN as_impression_type LIKE 'programmatic%' AND as_supply_path = 'unknown' AND g_partition_0 IS NOT NULL THEN g_market_type "
        "       ELSE m_market_type "
        "   END AS as_market_type, "
        "   as_ssp, "
        "   as_page_type, "
        "   g_partition_0, "
        "   g_partition_1, "
        "   g_partition_2, "
        "   g_partition_3, "
        "   date, "
        "   hour "
        "FROM ( "
        "   SELECT "
        "       m_auction_id, "
        "       m_market_type, "
        "       CASE "
        "           WHEN m_imp_type = 1 AND (g_auction_id IS NULL OR g_advertiser_id = 275205034 OR g_advertiser_id = 0) THEN 'blank' "
        "           WHEN m_imp_type = 1 AND g_auction_id IS NOT NULL AND g_advertiser_id != 275205034 AND g_advertiser_id != 0 THEN 'timeout (blank)' "
        "           WHEN m_imp_type = 2 THEN 'psa' "
        "           WHEN m_imp_type = 4 AND g_auction_id IS NOT NULL AND g_advertiser_id != 275205034 AND g_advertiser_id != 0 THEN 'timeout (default)' "
        "           WHEN m_imp_type = 5 AND (g_auction_id IS NULL OR g_advertiser_id = 275205034 OR g_advertiser_id = 0) AND (as_supply_path = 'xandr_prebid' OR as_supply_path = 'xandr_psp') THEN 'programmatic (xandr_prebid_psp)' "
        "           WHEN m_imp_type = 5 AND m_advertiser_id IN (2433812, 2191084) THEN 'programmatic (gam)' "
        "           WHEN m_imp_type = 5 AND g_auction_id IS NOT NULL AND g_advertiser_id != 275205034 AND g_advertiser_id != 0 THEN 'programmatic (gam)' "
        "           WHEN m_imp_type = 5 AND (g_auction_id IS NULL OR g_advertiser_id = 275205034 OR g_advertiser_id = 0) THEN 'direct (xandr)' "
        "           WHEN m_imp_type = 6 AND (g_auction_id IS NULL OR g_advertiser_id = 275205034 OR g_advertiser_id = 0) THEN 'programmatic (xandr_resold)' "
        "           WHEN m_imp_type = 6 AND g_auction_id IS NOT NULL AND g_advertiser_id != 275205034 AND g_advertiser_id != 0 THEN 'timeout (resold)' "
        "           WHEN m_imp_type = 7 THEN 'programmatic (rtb)' "
        "           WHEN m_advertiser_name LIKE 'EW_%' THEN 'inhouse' "
        "           WHEN m_buyer_member_id = 7823 AND m_line_item_subtype = 'house_default' AND (g_auction_id IS NULL OR g_advertiser_id = 275205034) THEN 'inhouse' "
        "           ELSE 'other' "
        "       END AS as_impression_type, "
        "       as_supply_type, "
        "       as_supply_path, "
        "       CASE "
        "           WHEN g_advertiser_id = 204872314 THEN 'AdX' "
        "           WHEN "
        "               m_buyer_member_id = 9642 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'ix') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'ix') OR "
        "               g_advertiser_id = 4656735805"
        "           THEN 'Index Exchange' "
        "           WHEN "
        "               m_buyer_member_id = 9644 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'openx') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'openx') OR "
        "               g_advertiser_id = 4575397897 "
        "           THEN 'OpenX' "
        "           WHEN "
        "               m_buyer_member_id = 9645 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'pubmatic') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'pubmatic') OR "
        "               g_advertiser_id = 4603863006 "
        "           THEN 'Pubmatic' "
        "           WHEN "
        "               m_buyer_member_id = 9646 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'rubicon') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'rubicon') OR "
        "               g_advertiser_id = 4672463259 "
        "           THEN 'Magnite' "
        "           WHEN "
        "               g_advertiser_id = 5071566697 OR "
        "               m_advertiser_id = 5645573 "
        "           THEN 'ScreenOnDemand' "
        "           WHEN g_advertiser_id = 4699099415 THEN 'Smaato' "
        "           WHEN "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'smartx') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'smartx') "
        "           THEN 'SmartX' "
        "           WHEN "
        "               m_buyer_member_id = 12158 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'smartadserver') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'smartadserver') OR "
        "               g_advertiser_id IN (4811883051, 252065554) "
        "           THEN 'Equativ' "
        "           WHEN "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'triplelift') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'triplelift') OR "
        "               g_advertiser_id = 4800156889 "
        "           THEN 'TripleLift' "
        "           WHEN "
        "               m_buyer_member_id = 12880 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'adf') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'adf') OR "
        "               g_advertiser_id = 5154154476 "
        "           THEN 'Adform' "
        "           WHEN "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'aduptech') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'aduptech') OR "
        "               m_advertiser_id = 1980120 "
        "           THEN 'AdUp' "
        "           WHEN "
        "               m_advertiser_id = 2523505 OR "
        "               g_advertiser_id = 251647354 "
        "           THEN 'Amazon' "
        "           WHEN "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'criteo') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'criteo') OR "
        "               g_advertiser_id = 251736034 "
        "           THEN 'Criteo' "
        "           WHEN "
        "               m_buyer_member_id = 10851 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'improvedigital') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'improvedigital') "
        "           THEN 'ImproveDigital' "
        "           WHEN "
        "               m_buyer_member_id = 12328 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'orbidder') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'orbidder') "
        "           THEN 'Otto' "
        "           WHEN "
        "               m_advertiser_id = 2248323 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'teads') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'teads') OR "
        "               g_advertiser_id IN (5359951838, 4695742704) "
        "           THEN 'Teads' "
        "           WHEN "
        "               m_buyer_member_id = 11999 OR "
        "               (as_supply_path = 'xandr_prebid' AND g_hb_bidder = 'yieldlab') OR "
        "               (as_supply_path = 'gam_prebid' AND g_hb_bidder = 'yieldlab') OR "
        "               g_advertiser_id = 5612153217 "
        "           THEN 'Yieldlab' "
        "           ELSE 'other' "
        "       END AS as_ssp, "
        "       g_market_type, "
        "       as_page_type, "
        "       g_partition_0, "
        "       g_partition_1, "
        "       g_partition_2, "
        "       g_partition_3, "
        "       concat(m_partition_0, '-', m_partition_1, '-', m_partition_2) AS date, "
        "       m_partition_3 AS hour "
        "   FROM ( "
        "       SELECT "
        "           m_auction_id, "
        "           m_imp_type, "
        "           m_advertiser_id, "
        "           m_advertiser_name, "
        "           m_buyer_member_id, "
        "           m_market_type, "
        "           m_line_item_subtype, "
        "           g_auction_id, "
        "           g_advertiser_id, "
        "           g_market_type, "
        "           g_hb_bidder, "
        "           CASE "
        "               WHEN m_placements_name LIKE '%-desktop%' THEN 'web' "
        "               WHEN m_placements_name LIKE '%-mew%' THEN 'm_web' "
        "               WHEN m_placements_name LIKE '%-amp%' THEN 'amp' "
        "               WHEN m_placements_name LIKE '%-app%' THEN 'app' "
        "               ELSE null "
        "           END AS as_supply_type, "
        "           CASE "
        "               WHEN g_advertiser_id IN (4575397897, 4656735805, 4603863006, 4672463259, 4699099415, 4811883051, 4800156889, 4766976384) THEN 'gam_ebda' "
        "               WHEN g_advertiser_id = 4541722774 THEN 'gam_prebid' "
        "               WHEN g_advertiser_id = 251647354 THEN 'gam_amazonTAM' "
        "               WHEN g_advertiser_id = 204872314 THEN 'gam_programmatic' "
        "               WHEN m_advertiser_id IN (2433812, 2191084) AND g_advertiser_id NOT IN (0, 275205034, 4541722774, 204872314, 251647354) THEN 'gam_gpt' "
        "               WHEN g_advertiser_id != 275205034 AND g_advertiser_id != 0 THEN 'gam_other' "
        "               WHEN m_buyer_member_id IN (9642, 9645, 9646, 12880, 12328, 12158, 9644, 10851, 11999) OR m_buyer_member_name LIKE '%(PSP)' THEN 'xandr_psp' "
        "               WHEN m_advertiser_id IN (2379688, 2523505, 2270325, 1980120, 2248323, 5645573) THEN 'xandr_prebid' "
        "               WHEN m_advertiser_id NOT IN (9115624, 2433812, 2191084, 2379688, 2523505, 2270325, 1980120, 2248323, 5645573) THEN 'xandr_ast' "
        "               ELSE 'unknown' "
        "           END AS as_supply_path, "
        "           regexp_extract(regexp_replace(m_placements_name, '(-[a-z]*(_([a-z]*|\d*))?)$', ''), '_index|_home_index|_story|_video') as as_page_type, "
        "           m_partition_0, "
        "           m_partition_1, "
        "           m_partition_2, "
        "           m_partition_3, "
        "           g_partition_0, "
        "           g_partition_1, "
        "           g_partition_2, "
        "           g_partition_3 "
        "       FROM ( "
        #           build microsoft_data
        "           SELECT "
        "               m_auction_id, "
        "               m_imp_type, "
        "               m_advertiser_id, "
        "               m_advertiser_name, "
        "               m_buyer_member_id, "
        "               m_buyer_member_name, "
        "               CASE "
        "                   WHEN m_deal_id = 0 THEN 'OMP (MICROSOFT)' "
        "                   WHEN an_deals.curated_deal = TRUE THEN 'MP (MICROSOFT)' "
        "                   ELSE 'PMP (MICROSOFT)' "
        "               END AS m_market_type, "
        "               m_placements_name, "
        "               m_line_item_subtype, "
        "               m_partition_0, "
        "               m_partition_1, "
        "               m_partition_2, "
        "               m_partition_3 "
        "           FROM ( "
        "               SELECT "
        "                   auction_id_64   as m_auction_id, "
        "                   imp_type        as m_imp_type, "
        "                   advertiser_id   as m_advertiser_id, "
        "                   buyer_member_id as m_buyer_member_id, "
        "                   tag_id          as m_tag_id, "
        "                   deal_id         as m_deal_id, "
        "                   line_item_id    as m_line_item_id, "
        "                   partition_0     as m_partition_0, "
        "                   partition_1     as m_partition_1, "
        "                   partition_2     as m_partition_2, "
        "                   partition_3     as m_partition_3 "
        "               FROM standard_feed_parquet_temp "
        f"              WHERE partition_0 = '{m_partition[0][0:4]}' AND partition_1 = '{m_partition[0][5:7]}' "
        f"                  AND partition_2 = '{m_partition[0][8:10]}' AND partition_3 = '{m_partition[1]}' "
        "           ) "
        "           LEFT JOIN ( "
        "               SELECT "
        "                   id, "
        "                   CASE "
        "                       WHEN CARDINALITY(FILTER(buyer_seats, element -> element.name LIKE '%CURATOR%')) > 0 THEN TRUE "
        "                       ELSE FALSE "
        "                   END AS curated_deal "
        "               FROM \"datalake_raw\".\"an_deals\" "
        "               WHERE id != 0 "
        "           ) as an_deals "
        "           ON m_deal_id = an_deals.id "
        "           LEFT JOIN ( "
        "               SELECT id, name as m_placements_name "
        "               FROM \"datalake_raw\".\"an_placements\" "
        "           ) as an_placements "
        "           ON m_tag_id = an_placements.id "
        "           LEFT JOIN ( "
        "               SELECT id, line_item_subtype as m_line_item_subtype "
        "               FROM \"datalake_raw\".\"an_line_items\" "
        "           ) as an_line_items "
        "           ON m_line_item_id = an_line_items.id "
        "           LEFT JOIN ( "
        "               SELECT buyer_member_id, buyer_member_name as m_buyer_member_name "
        "               FROM \"datalake_raw\".\"an_buyer_member\" "
        "               WHERE CAST(buyer_member_id AS integer) != 0 AND CAST(buyer_member_id AS integer) != 229 AND buyer_member_name != 'Default' "
        "           ) as an_buyer_members "
        "           ON CAST(m_buyer_member_id as varchar) = an_buyer_members.buyer_member_id "
        "           LEFT JOIN ( "
        "               SELECT "
        "                   id, "
        "                   name as m_advertiser_name "
        "               FROM \"datalake_raw\".\"an_advertisers\" "
        "           ) as an_advertisers_df "
        "           ON m_advertiser_id = an_advertisers_df.id "
        "       ) as microsoft_data "
        "       LEFT JOIN ( "
        #           build google_data
        "           SELECT "
        "               CAST(g_auction_id as bigint) as g_auction_id, "
        "               g_advertiser_id, "
        "               g_hb_bidder, "
        "               google_companies.name as g_company_name, "
        "               CASE "
        "                   WHEN "
        "                       g_advertiser_id IN (4575397897, 4656735805, 4603863006, 4672463259, 4699099415, 4811883051, 4800156889, 4766976384) OR "
        "                       google_companies.name LIKE '%EBDA_%' "
        "                   THEN 'PMP (GOOGLE)' "
        "                   WHEN g_deal_id IS NULL THEN 'OMP (GOOGLE)' "
        "                   ELSE 'PMP (GOOGLE)' "
        "               END as g_market_type, "
        "               g_partition_0, "
        "               g_partition_1, "
        "               g_partition_2, "
        "               g_partition_3 "
        f"          FROM ( {union_google_select_statements} ) "
        "           LEFT JOIN ( "
        "              SELECT id, name "
        "               FROM \"datalake_raw\".\"google_companies\" "
        "           ) as google_companies "
        "           ON g_advertiser_id = google_companies.id "
        "      ) as google_data "
        "       ON m_auction_id = g_auction_id "
        "   ) "
        ")"
    )

    event['query'] = {}
    event['query']['QueryString'] = query_string

    return event
