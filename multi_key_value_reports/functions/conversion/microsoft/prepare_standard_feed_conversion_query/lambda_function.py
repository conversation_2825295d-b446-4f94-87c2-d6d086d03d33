def lambda_handler(event, context):
    event['query'] = (
        "INSERT INTO standard_feed_parquet_temp "
        "( "
        "  auction_id_64, "
        "  site_id, "
        "  tag_id, "
        "  imp_type, "
        "  view_result, "
        "  revenue, "
        "  creative_width, "
        "  creative_height, "
        "  publisher_id, "
        "  device_type, "
        "  line_item_id, "
        "  user_id_64, "
        "  advertiser_id, "
        "  buyer_member_id, "
        "  deal_id, "
        "  partition_0, "
        "  partition_1, "
        "  partition_2, "
        "  partition_3 "
        ") "
        "SELECT  "
        "  auction_id_64, "
        "  site_id, "
        "  tag_id, "
        "  imp_type, "
        "  view_result, "
        "  (seller_revenue_cpm / 1000) as revenue, "
        "  creative_width, "
        "  creative_height, "
        "  publisher_id, "
        "  device_type, "
        "  line_item_id, "
        "  user_id_64, "
        "  advertiser_id, "
        "  buyer_member_id, "
        "  deal_id, "
        "  partition_0, "
        "  partition_1, "
        "  partition_2, "
        "  partition_3 "
        "FROM standard_feed "
        f"WHERE partition_0 = '{event["partition_0"]}' "
        f"AND partition_1 = '{event["partition_1"]}' "
        f"AND partition_2 = '{event["partition_2"]}' "
        f"AND partition_3 = '{event["partition_3"]}' "
        "AND event_type = 'imp'"
    )

    return {
        "query": event["query"]
    }
