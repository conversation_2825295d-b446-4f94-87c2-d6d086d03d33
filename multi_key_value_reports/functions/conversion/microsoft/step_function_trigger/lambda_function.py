import json
import boto3
import os

s3_client = boto3.resource('s3')
step_functions_client = boto3.client('stepfunctions')


def lambda_handler(event, context):
    bucket_name = event['Records'][0]['s3']['bucket']['name']
    object_key = event['Records'][0]['s3']['object']['key']

    content_object = s3_client.Object(bucket_name, object_key)
    file_content = content_object.get()['Body'].read().decode('utf-8')

    path = json.loads(file_content)['path']
    path_split = path.split('/')

    event = {
        'bucket_name': f'{bucket_name}',
        'table_name': f'{path_split[2]}',
        'date': f"'{path_split[3]}-{path_split[4]}-{path_split[5]}'",
        'path': f'{path}',
        'partition_0': f'{path_split[3]}',
        'partition_1': f'{path_split[4]}',
        'partition_2': f'{path_split[5]}',
        'partition_3': f'{path_split[6]}',
        'partition_4': f'{path_split[7]}'
    }

    step_functions_client.start_execution(
        stateMachineArn=os.environ['STATE_MACHINE_ARN'],
        input=json.dumps(event)
    )
