import os
from datetime import datetime, timedelta


def lambda_handler(event, context):
    date = f"{event["partition_0"]}-{event["partition_1"]}-{event["partition_2"]}"
    hour = f"{event["partition_3"]}"
    current_partition = datetime.strptime(f"{date} {hour}", "%Y-%m-%d %H")
    previous_partition = current_partition - timedelta(hours=1)
    next_partition = current_partition + timedelta(hours=1)

    event["query"] = (
        f"INSERT INTO {os.environ.get("TABLE_MULTI_KEY_VALUE_MICROSOFT")} "
        "( "
        "    auction_id_64, "
        "    publisher_id, "
        "    site_id, "
        "    tag_id, "
        "    imp_type, "
        "    device_type, "
        "    creative_size, " 
        "    imps, "
        "    imps_viewed, "
        "    imps_view_measured, "
        "    revenue, "
        "    key_values, "
        "    line_item_id, "
        "    user_id_64, "
        "    clicks, "
        "    date, "
        "    hour "
        ") "
        "SELECT "
        "   s.auction_id_64, "
        "   s.publisher_id, "
        "   s.site_id, "
        "   s.tag_id, "
        "   s.imp_type, "
        "   s.device_type, "
        "   concat_ws('x', cast(s.creative_width as VARCHAR), cast(s.creative_height as VARCHAR)), "
        "   1 as imps, "
        "   case  "
        "     when s.view_result = 1 "
        "     then 1 "
        "     else 0 "
        "   end as imps_viewed, "
        "   case  "
        "     when s.view_result in (0, 1, 2) "
        "     then 1 "
        "     else 0 "
        "   end as imps_view_measured, "
        "   s.revenue, "
        "   a.key_values, "
        "   s.line_item_id, "
        "   s.user_id_64, "
        "   CASE WHEN EXISTS ( "
        "      SELECT auction_id_64 FROM clicks_parquet_temp c "
        "      WHERE c.auction_id_64 = s.auction_id_64 "
        "         AND s.partition_0 = c.partition_0 "
        "         AND s.partition_1 = c.partition_1 "
        "         AND s.partition_2 = c.partition_2 "
        "         AND s.partition_3 = c.partition_3 "
        "   ) THEN 1 "
        "   ELSE 0 "
        "   END AS clicks, "
        f"  CAST('{date}' AS VARCHAR) as date, "
        f"  CAST('{hour}' AS VARCHAR) as hour "
        "FROM auction_kv_labels_feed_parquet_temp a "
        "JOIN standard_feed_parquet_temp s "
        "   on s.auction_id_64 = a.auction_id_64 "
        f"   WHERE s.partition_0 = '{current_partition.year:04d}' "
        f"       AND s.partition_1 = '{current_partition.month:02d}' "
        f"       AND s.partition_2 = '{current_partition.day:02d}' "
        f"       AND s.partition_3 = '{current_partition.hour:02d}' "
        "       AND ("
        "               ("
        f"                  a.partition_0 = '{current_partition.year:04d}' "
        f"                  AND a.partition_1 = '{current_partition.month:02d}' "
        f"                  AND a.partition_2 = '{current_partition.day:02d}' "
        f"                  AND a.partition_3 = '{current_partition.hour:02d}'"
        "               ) "
        "               OR ("
        f"                  a.partition_0 = '{previous_partition.year:04d}' "
        f"                  AND a.partition_1 = '{previous_partition.month:02d}' "
        f"                  AND a.partition_2 = '{previous_partition.day:02d}' "
        f"                  AND a.partition_3 = '{previous_partition.hour:02d}'"
        "               ) "
        "               OR ("
        f"                  a.partition_0 = '{next_partition.year:04d}' "
        f"                  AND a.partition_1 = '{next_partition.month:02d}' "
        f"                  AND a.partition_2 = '{next_partition.day:02d}' "
        f"                  AND a.partition_3 = '{next_partition.hour:02d}'"
        "               )"
        "           )"
    )

    return {
        "query": event["query"]
    }
