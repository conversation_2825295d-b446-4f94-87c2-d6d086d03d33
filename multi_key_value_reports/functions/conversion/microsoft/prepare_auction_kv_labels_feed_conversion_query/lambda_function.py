def lambda_handler(event, context):
    event['query'] = (
        "INSERT INTO auction_kv_labels_feed_parquet_temp "
        "SELECT auction_id_64, "
        "   map(array_agg(\"key\"), array_agg(\"value\")) AS key_values, "
        f"  '{event["partition_0"]}', "
        f"  '{event["partition_1"]}', "
        f"  '{event["partition_2"]}', "
        f"  '{event["partition_3"]}' "
        "FROM ( "
        "   SELECT "
        "       auction_id_64, "
        "       key, "
        "       array_agg(\"value\") AS value "
        "   FROM auction_kv_labels_feed "
        f"  WHERE partition_0 = '{event["partition_0"]}' "
        f"      AND partition_1 = '{event["partition_1"]}' "
        f"      AND partition_2 = '{event["partition_2"]}' "
        f"      AND partition_3 = '{event["partition_3"]}' "
        "       AND key != '1plusx' "
        "       AND key != 'opectx' "
        "   GROUP BY auction_id_64, key "
        ") "
        "GROUP BY auction_id_64"
    )

    return {
        "query": event["query"]
    }
