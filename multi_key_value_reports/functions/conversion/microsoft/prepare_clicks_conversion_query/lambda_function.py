def lambda_handler(event, context):
    event['query'] = (
        "INSERT INTO clicks_parquet_temp "
        "( "
        "  auction_id_64, "
        "  partition_0, "
        "  partition_1, "
        "  partition_2, "
        "  partition_3 "
        ") "
        "SELECT  "
        "  auction_id_64, "
        "  partition_0, "
        "  partition_1, "
        "  partition_2, "
        "  partition_3 "
        "FROM standard_feed "
        f"WHERE partition_0 = '{event["partition_0"]}' "
        f"AND partition_1 = '{event["partition_1"]}' "
        f"AND partition_2 = '{event["partition_2"]}' "
        f"AND partition_3 = '{event["partition_3"]}' "
        "AND event_type = 'click'"
    )

    return {
        "query": event["query"]
    }
