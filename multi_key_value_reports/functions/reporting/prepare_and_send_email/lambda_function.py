import json
import os
import smtplib
from enum import Enum
from datetime import datetime, timedelta

import boto3
import duckdb

from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

conn = duckdb.connect(database=':memory:', read_only=False)

conn: duckdb.DuckDBPyConnection = conn.execute(
    """
    CREATE OR REPLACE SECRET secret (
        TYPE S3,
        PROVIDER CREDENTIAL_CHAIN,
        REGION 'eu-central-1',
        ENDPOINT 's3.eu-central-1.amazonaws.com'
    );
    """
)

ssm_client = boto3.client(
    service_name="ssm",
    region_name="eu-central-1"
)

s3_client = boto3.client("s3")

API_SECRET = json.loads(ssm_client.get_parameter(
    Name="/workmail/noreply",
    WithDecryption=True
)["Parameter"]["Value"])

DOWNLOAD_LINK_TEMPLATE = "<p>https://{domain_name}/?id={report_id}&report={download_name}</p>"
BODY_HTML = """<html>
    <head></head>
    <body>
     <p>ID: {report_id}</p>
     <p>Name: {report_name}</p>
     <p>Requested Start date: {start_date}</p>
     <p>Requested End date: {end_date}</p>
     <p>Data sources: {data_sources}</p>
     <p>Download link:</p>
     {download_link}
    </body>
    </html>
    """


class ReportTimespan(Enum):
    LAST_DAY = "LAST_DAY",
    LAST_MONTH = "LAST_MONTH",
    REPORTING_LIFETIME = "REPORTING_LIFETIME",


def __get_query_with_dates(processing_bucket_name, report_id, start_date, end_date):
    return f"""
        SELECT * from read_csv('s3://{processing_bucket_name}/generated_reports/{report_id}/**', header = true)
        where CAST(date as DATE) >= '{start_date}' AND CAST(date as DATE) <= '{end_date}'
        order by date
    """


def __create_message(report, api_secret, download_link):
    message = MIMEMultipart()
    message["Subject"] = f"[Insighter] {report['report_name']}"
    message["From"] = "Insighter <" + api_secret['email'] + ">"
    part = MIMEText(BODY_HTML.format(
        report_id=report["report_id"],
        report_name=report["report_name"],
        start_date=report["start_date"],
        end_date=report["end_date"],
        data_sources=", ".join(report["data_sources"]),
        download_link=download_link
    ), "html")
    message.attach(part)
    return message


def __handle_reporting_lifetime(report, processing_bucket_name, domain_name):
    query = __get_query_with_dates(
        processing_bucket_name, report["report_id"], report["start_date"], report["end_date"]
    )
    conn.sql(query).write_csv(f"s3://{processing_bucket_name}/downloads/{report['report_id']}/{report['report_id']}.csv")
    download_link = DOWNLOAD_LINK_TEMPLATE.format(
        domain_name=domain_name,
        report_id=report["report_id"],
        download_name=report["report_id"]
    )
    return __create_message(report, API_SECRET, download_link)


def __handle_last_day(report, processing_bucket_name, domain_name):
    processed_dates = report["processed_dates"]
    download_links = []
    for processed_date in processed_dates:
        download_name = f'{report["report_id"]}-{processed_date}'
        download_path = f"downloads/{report['report_id']}/{download_name}.csv"
        copy_source = {
            'Bucket': processing_bucket_name,
            'Key': f'generated_reports/{report["report_id"]}/{processed_date}.csv'
        }
        s3_client.copy(copy_source, processing_bucket_name, download_path)
        download_links.append(DOWNLOAD_LINK_TEMPLATE.format(
            domain_name=domain_name,
            report_id=report["report_id"],
            download_name=download_name
        ))
    return __create_message(report, API_SECRET, "".join(download_links))


def __handle_last_month(report, processing_bucket_name, domain_name):
    today = datetime.today()
    last_day_of_last_month = today.replace(day=1) - timedelta(days=1)
    first_day_of_last_month = last_day_of_last_month.replace(day=1)
    last_month = first_day_of_last_month.strftime("%Y-%m")
    last_day_of_second_last_month = first_day_of_last_month - timedelta(days=1)

    delivered_reports = s3_client.list_objects_v2(
        Bucket=processing_bucket_name,
        Prefix=f'downloads/{report["report_id"]}/',
    )
    if len(delivered_reports["Contents"]) > 0 and delivered_reports["Contents"][0]["LastModified"].month == today.month:
        return None

    generated_reports = s3_client.list_objects_v2(
        Bucket=processing_bucket_name,
        Prefix=f'generated_reports/{report["report_id"]}/',
        StartAfter=f'generated_reports/{report["report_id"]}/{last_day_of_second_last_month.strftime("%Y-%m-%d")}.csv'
    )
    count = 0
    for report_item in generated_reports["Contents"]:
        if report_item["Key"].startswith(f"generated_reports/{report['report_id']}/{last_month}"):
            count += 1
    if count != (last_day_of_last_month - first_day_of_last_month).days + 1:
        return None

    query = __get_query_with_dates(
        processing_bucket_name, report["report_id"],
        first_day_of_last_month.strftime("%Y-%m-%d"),
        last_day_of_last_month.strftime("%Y-%m-%d")
    )
    conn.sql(query).write_csv(f"s3://{processing_bucket_name}/downloads/{report['report_id']}/{report['report_id']}.csv")
    download_link = DOWNLOAD_LINK_TEMPLATE.format(
        domain_name=domain_name,
        report_id=report["report_id"],
        download_name=report["report_id"]
    )
    return __create_message(report, API_SECRET, download_link)


def __send_email(message, recipients):
    s = smtplib.SMTP_SSL(host=os.environ['smtp_host'], port=int(os.environ['smtp_port']))
    s.login(API_SECRET["email"], API_SECRET["password"])
    s.send_message(message, to_addrs=recipients)


def lambda_handler(event, context):
    processing_bucket_name = os.environ.get("PROCESSING_BUCKET_NAME")
    domain_name = os.environ.get("DOMAIN_NAME")
    for report in event["reports"]:
        report_timespan = report["report_timespan"]
        message = None

        if report_timespan == ReportTimespan.REPORTING_LIFETIME.name:
            message = __handle_reporting_lifetime(report, processing_bucket_name, domain_name)
        elif report_timespan == ReportTimespan.LAST_DAY.name:
            message = __handle_last_day(report, processing_bucket_name, domain_name)
        elif report_timespan == ReportTimespan.LAST_MONTH.name:
            message = __handle_last_month(report, processing_bucket_name, domain_name)

        if message and "recipients" in report and report["recipients"]:
            __send_email(message, report["recipients"])

    return event
