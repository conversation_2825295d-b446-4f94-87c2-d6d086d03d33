import os
from datetime import datetime, timedelta

import boto3
from strictyaml import load

from insighter_commons import REPORT_DEFINITION_SCHEMA
from insighter_commons.constants import DATA_SOURCE_GOOGLE, DATA_SOURCE_MICROSOFT, LOAD_CREDENTIALS_STATEMENT

s3_client = boto3.client("s3")
ecs_client = boto3.client("ecs")

TABLE_MULTI_KEY_VALUE_GOOGLE = os.environ.get("TABLE_MULTI_KEY_VALUE_GOOGLE")
TABLE_MULTI_KEY_VALUE_MICROSOFT = os.environ.get("TABLE_MULTI_KEY_VALUE_MICROSOFT")



def __file_exists(bucket, prefix):
    s3_paginator = s3_client.get_paginator('list_objects_v2')
    for page in s3_paginator.paginate(Bucket=bucket, Prefix=prefix):
        if page['KeyCount'] > 0:
            return True
    return False


def __day_complete(bucket, prefix):
    s3_paginator = s3_client.get_paginator('list_objects_v2')
    for page in s3_paginator.paginate(Bucket=bucket, Prefix=prefix, Delimiter='/'):
        if page['KeyCount'] == 24:
            return True
    return False


def __data_sources_day_complete(data_sources, bucket, date):
    if DATA_SOURCE_GOOGLE in data_sources:
        if not __day_complete(
                bucket,
                f"{TABLE_MULTI_KEY_VALUE_GOOGLE}/partition_0={date[0:4]}/partition_1={date[5:7]}/partition_2={date[8:10]}/"
        ):
            return False
    if DATA_SOURCE_MICROSOFT in data_sources:
        if not __day_complete(bucket, f"{TABLE_MULTI_KEY_VALUE_MICROSOFT}/date={date}/"):
            return False
    return True


def __construct_sql(sql, date, bucket_name, data_sources):
    if DATA_SOURCE_MICROSOFT in data_sources and DATA_SOURCE_GOOGLE in data_sources:
        sql = sql.format(
            date=date,
            bucket_name=bucket_name,
            multi_key_value_microsoft=TABLE_MULTI_KEY_VALUE_MICROSOFT,
            multi_key_value_google=TABLE_MULTI_KEY_VALUE_GOOGLE
        )
    elif DATA_SOURCE_MICROSOFT in data_sources:
        sql = sql.format(
            date=date,
            bucket_name=bucket_name,
            multi_key_value_microsoft=TABLE_MULTI_KEY_VALUE_MICROSOFT
        )
    elif DATA_SOURCE_GOOGLE in data_sources:
        sql = sql.format(
            date=date,
            bucket_name=bucket_name,
            multi_key_value_google=TABLE_MULTI_KEY_VALUE_GOOGLE
        )
    sql = " ".join(
        sql.replace('\n', ' ').split()
    )
    return sql


def __create_ecs_task_definition(report_id, date, sql, output_path, ecs_memory):
    task_definition = ecs_client.register_task_definition(
        family=f"DuckdbTaskDefinition-{datetime.now().strftime("%Y-%m-%d")}-{report_id}",
        taskRoleArn=os.environ.get("taskRoleArn"),
        executionRoleArn=os.environ.get("executionRoleArn"),
        networkMode='awsvpc',
        cpu='16384',
        memory=ecs_memory,
        requiresCompatibilities=[
            'FARGATE'
        ],
        tags=[
            {
                'key': 'App',
                'value': 'insighter'
            },
            {
                'key': 'InsighterReportId',
                'value': report_id
            }
        ],
        containerDefinitions=[
            {
                "command": [
                    ":memory",
                    f"{LOAD_CREDENTIALS_STATEMENT}; COPY ({sql}) TO '{output_path}';"
                ],
                "name": f"{report_id}-{date}",
                "image": "083168087794.dkr.ecr.eu-central-1.amazonaws.com/insighter:1.2.1",
                "essential": True,
                "logConfiguration": {
                    "logDriver": "awslogs",
                    "options": {
                        "awslogs-group": os.environ.get("log_group_name"),
                        "awslogs-region": "eu-central-1",
                        "awslogs-stream-prefix": "ecs"
                    }
                }
            }
        ]
    )
    return task_definition['taskDefinition']['taskDefinitionArn']


def lambda_handler(event, context):
    processing_bucket_name = os.environ.get("PROCESSING_BUCKET_NAME")

    definitions = list()
    processed_dates = list()
    response = s3_client.get_object(Bucket=processing_bucket_name, Key=event['key'])
    config = load(response["Body"].read().decode('utf-8'), REPORT_DEFINITION_SCHEMA).data
    report_id = event['key'].split("/")[-1].split(".")[0]
    report_name = config['name']

    report_timespan = config['report_timespan']

    start_date = config['start_date']
    end_date = config['end_date']

    end_date = end_date if end_date else datetime.now() - timedelta(days=1)
    delta = end_date - start_date

    data_sources = config['data_sources']

    ecs_memory = config['ecs_memory']

    for i in range(delta.days + 1):
        date = (start_date + timedelta(days=i)).strftime("%Y-%m-%d")
        output_key = f"generated_reports/{report_id}/{date}.csv"
        output_path = f"s3://{processing_bucket_name}/{output_key}"

        sql = __construct_sql(config['sql'], date, processing_bucket_name, data_sources)

        if __file_exists(processing_bucket_name, output_key):
            # report already processed
            continue
        if not __data_sources_day_complete(data_sources, processing_bucket_name, date):
            continue

        task_definition_arn = __create_ecs_task_definition(report_id, date, sql, output_path, ecs_memory)
        definitions.append(task_definition_arn)
        processed_dates.append(date)

    return {
        "report_id": report_id,
        "report_name": report_name,
        "task_definitions": definitions,
        "recipients": config['notification_recipients'] if 'notification_recipients' in config else None,
        "start_date": start_date.strftime("%Y-%m-%d"),
        "end_date": end_date.strftime("%Y-%m-%d"),
        "report_timespan": report_timespan,
        "data_sources": data_sources,
        "processed_dates": processed_dates,
        # for checking if report will expire
        "report_definition_end_date": config['end_date'].strftime("%Y-%m-%d") if config['end_date'] else None,
    }
