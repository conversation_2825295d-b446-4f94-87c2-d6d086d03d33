import os
import boto3

s3_client = boto3.client("s3")


def lambda_handler(event, context):
    processing_bucket_name = os.environ.get("PROCESSING_BUCKET_NAME")
    objects = s3_client.list_objects_v2(
        Bucket=processing_bucket_name,
        Prefix=os.environ["CONFIGURATIONS_PREFIX"]
    )
    configurations = list()
    for obj in objects["Contents"]:
        key = obj["Key"]
        if key.endswith(".yaml"):
            configurations.append(
                {
                    "BUCKET_NAME": processing_bucket_name,
                    "key": obj["Key"]
                }
            )
    return {
        "reports": configurations
    }
