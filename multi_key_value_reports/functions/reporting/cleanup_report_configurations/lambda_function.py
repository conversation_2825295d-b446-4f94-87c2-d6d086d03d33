import os
import boto3
from datetime import datetime, timedelta


s3_client = boto3.client("s3")


def lambda_handler(event, context):
    processing_bucket_name = os.environ.get("PROCESSING_BUCKET_NAME")
    report_prefix = os.environ["CONFIGURATIONS_PREFIX"]
    for report in event["reports"]:
        report_id = report["report_id"]
        end_date = report["report_definition_end_date"]
        will_expire = True if end_date and datetime.strptime(end_date, "%Y-%m-%d").date() <= datetime.now().date() - timedelta(days=1) else False

        if will_expire:
            destination_path = f"{report_prefix}expired/{report_id}.yaml_expired"
            source_path = f'{report_prefix}{report_id}.yaml'
            copy_source = {
                'Bucket': processing_bucket_name,
                'Key': source_path
            }
            s3_client.copy(copy_source, processing_bucket_name, destination_path)
            s3_client.delete_object(Bucket=processing_bucket_name, Key=source_path)

    return event
