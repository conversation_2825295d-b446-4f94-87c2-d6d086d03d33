import json
import os

import botocore
from flask import Flask, redirect, request, abort
from flask_oidc import OpenIDConnect
import boto3

ssm_client = boto3.client(
    service_name="ssm",
    region_name="eu-central-1"
)

CLIENT_SECRETS = json.loads(ssm_client.get_parameter(
    Name=os.environ.get("OAUTH_PARAMETER_NAME"),
    WithDecryption=True
)["Parameter"]["Value"])

ALLOWED_REDIRECT_DOMAIN_PREFIX = "https://business-integration-multi-key-value-report.s3.amazonaws.com/downloads/"

app = Flask(__name__)
app.config.update({
    'SECRET_KEY': os.environ.get("SESSION_SECRET_KEY"),
    'OIDC_CLIENT_SECRETS': CLIENT_SECRETS,
    'OIDC_ID_TOKEN_COOKIE_SECURE': False,
    'OIDC_USER_INFO_ENABLED': True,
    'OIDC_OPENID_REALM': 'adsolutions',
    'OIDC_SCOPES': ['openid', 'email'],
    'OIDC_INTROSPECTION_AUTH_METHOD': 'client_secret_post',
    'PREFERRED_URL_SCHEME': 'https'
})

oidc = OpenIDConnect()
oidc.init_app(app, None)


@app.route('/')
@oidc.require_login
def download_report():
    if report_exists(request.args.get('id'), request.args.get('report')):
        return redirect(
            ALLOWED_REDIRECT_DOMAIN_PREFIX + generate_presigned_url(request.args.get('id'), request.args.get('report')),
            code=302
        )
    abort(404)


def generate_presigned_url(report_id, report_name):
    s3_client = boto3.client("s3")

    return s3_client.generate_presigned_url(
        ClientMethod="get_object",
        Params={
            "Bucket": "business-integration-multi-key-value-report",
            "Key": f"downloads/{report_id}/{report_name}.csv"
        },
        ExpiresIn=600
    ).replace(ALLOWED_REDIRECT_DOMAIN_PREFIX, "")


def report_exists(report_id: str, report_name: str):
    s3_client = boto3.client("s3")
    try:
        s3_client.head_object(Bucket="business-integration-multi-key-value-report",
                              Key=f"downloads/{report_id}/{report_name}.csv")
        return True
    except botocore.exceptions.ClientError:
        return False


if __name__ == '__main__':
    app.run()
