import json
import os
import smtplib

import boto3

import smart_open

from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

ssm_client = boto3.client(
    service_name="ssm",
    region_name="eu-central-1"
)

API_SECRET = json.loads(ssm_client.get_parameter(
    Name="/workmail/noreply",
    WithDecryption=True
)["Parameter"]["Value"])

BODY_HTML = """<html>
    <head></head>
    <body>
    Partitions for the following dates are incomplete:<br>
     {lines}
    </body>
    </html>
    """


def lambda_handler(event, context):
    query_output_location = event['output']['QueryExecution']['ResultConfiguration']['OutputLocation']

    num_lines = 0
    content = list()
    with smart_open.smart_open(query_output_location, 'r') as result_file:
        for line in result_file:
            num_lines = num_lines + 1
            content.append(line)

    if num_lines > 1:
        message = MIMEMultipart()
        message["Subject"] = "[Insighter] Partition Monitoring"
        message["From"] = "Insighter <" + API_SECRET['email'] + ">"
        message["To"] = os.environ.get("RECIPIENTS")

        part = MIMEText(BODY_HTML.format(
            lines="<br>".join(content)
        ), "html")
        message.attach(part)

        s = smtplib.SMTP_SSL(host=os.environ['smtp_host'], port=int(os.environ['smtp_port']))
        s.login(API_SECRET["email"], API_SECRET["password"])
        s.send_message(message)
