import os
import boto3
import re
from google_ad_manager_client import create_cloud_storage_client


def download_and_store_file(cloud_storage_name, file_name, bucket_name, destination_file_name):
    # Set up GCS client
    storage_client = create_cloud_storage_client(os.environ.get("GOOGLE_CLOUD_STORAGE_PARAMETER"))

    # Set up S3 client
    s3_client = boto3.client('s3')

    # Download and upload file in chunks
    bucket = storage_client.bucket(cloud_storage_name)
    blob = bucket.blob(file_name)
    with blob.open("rb") as gcs_file:
        s3_client.upload_fileobj(gcs_file, bucket_name, destination_file_name)


def to_snake_case(name):
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def lambda_handler(event, context):
    gcs_cloud_storage_name = os.environ.get("CLOUD_STORAGE_NAME")
    gcs_file_name = event["file_name"]
    processing_bucket_name = os.environ.get("PROCESSING_BUCKET_NAME")
    pattern = r"(NetworkImpressions|NetworkBackfillImpressions|NetworkClicks|NetworkBackfillClicks|NetworkActiveViews|NetworkBackfillActiveViews)_\d+_(\d{8})_(\d{2})(_corrected)?\.gz"
    match = re.match(pattern, gcs_file_name)
    file_type = match.group(1)
    file_date = match.group(2)
    file_hour = match.group(3)
    destination_file_name = f'{os.environ.get("GOOGLE_RAW_DATA_S3_PATH")}{to_snake_case(file_type)}/{file_date[:4]}/{file_date[4:6]}/{file_date[6:]}/{file_hour}/{gcs_file_name}'

    # Download and store file
    download_and_store_file(gcs_cloud_storage_name, gcs_file_name, processing_bucket_name, destination_file_name)
