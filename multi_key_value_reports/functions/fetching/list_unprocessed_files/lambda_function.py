import os
from datetime import datetime, timedelta
from google_ad_manager_client import create_cloud_storage_client


def lambda_handler(event, context):
    # Set up GCS client
    client = create_cloud_storage_client(os.environ.get("GOOGLE_CLOUD_STORAGE_PARAMETER"))

    # List files with the specified prefix from bucket
    blobs = client.list_blobs(os.environ.get("CLOUD_STORAGE_NAME"), prefix=event["file_prefix"])

    # Filter files from the last hour
    current_time_utc = datetime.strptime(event["time"], "%Y-%m-%dT%H:%M:%SZ")
    last_hour_utc = current_time_utc - timedelta(hours=1)
    recent_files = [blob.name for blob in blobs
                    if datetime.strptime(str(blob.updated)[:19], "%Y-%m-%d %H:%M:%S") > last_hour_utc]
    event["unprocessed_files"] = recent_files
    return event
